# Status Generator 使用说明

一个强大的Python工具，用于将Markdown格式的状态报告转换为专业样式的HTML报告，支持高级格式化和链接处理功能。

## 🚀 主要功能

### 核心功能
- **多格式Markdown到HTML转换**，支持自定义样式
- **高级状态标签处理** (HIGH, MEDIUM, LOW, MED-HIGH, NOT STARTED)
- **多级嵌套列表处理**，自动缩进检测
- **智能表格生成**，从雷达列表部分生成表格
- **方向指示符**，支持可视化箭头 (↗ ↘)
- **双格式rdar链接支持**，无缝迁移

### rdar链接处理
- **新格式**: `rdar://146598443 (radar titles)` → `radar titles (rdar://146598443)`
- **旧格式**: `(rdar://146598443)` 或 `[rdar://146598443]` → 可点击链接
- **一致的样式**，两种格式都使用`s4` CSS类
- **自动检测**并转换混合格式

### 高级特性
- **可配置CSS样式**，专业默认样式
- **分段感知处理** (执行摘要、雷达列表、功能状态)
- **性能优化**，预编译正则表达式模式
- **完整HTML文档生成**，正确编码
- **跨平台兼容** (Python 2.7+ 和 3.x)

## 📦 可用版本

### 1. 精简版 (`status_generator_refactored.py`)
- **轻量快速** - 性能优化
- **核心功能** - 基本特性
- **简单命令行界面**
- **适用于**: 快速转换和自动化工作流

### 2. 完整功能版 (如果有其他版本)
- **完整功能集** - 高级处理
- **模块化架构** - 可扩展设计
- **丰富命令行选项** - 配置选项
- **自动浏览器打开** - 即时预览
- **适用于**: 复杂报告和交互使用

## 🛠 安装与使用

### 快速开始

```bash
# 使用主版本 (推荐大多数用户)
python3 status_generator_refactored.py

# 指定输入输出文件
python3 status_generator_refactored.py --md report.md --html output.html
```

### 命令行选项

```bash
python3 status_generator_refactored.py [选项]

选项:
  --md MD           输入Markdown文件 (默认: report.md)
  --html HTML       输出HTML文件 (默认: report.html)
  --css CSS         自定义CSS文件 (可选)
  --config CONFIG   自定义配置文件 (可选)
  --verbose         启用详细日志
  -h, --help        显示帮助信息
```
## 📋 Markdown文件编辑规范

### 文件结构要求

Markdown文件必须包含以下三个主要部分，每个部分以一级标题（#）开头：

1. **`# Feature readiness status`** - 功能就绪状态表格
2. **`# Executive Summary`** - 执行摘要
3. **`# Radar List`** - 雷达列表

### 1. Feature readiness status 部分

这部分包含一个表格，用于显示不同功能的测试状态。

#### 表格格式：
```markdown
# Feature readiness status
| Sival       | Dev                     | 功能描述        | 备注 |
|-------------|-------------------------|-----------------|------|
|testable     |testable                 |USB2 Host/Device Mode| |
|issue        |testable                 |Sleep/Wake       |rdar://141797028 |
|na           |blocked                  |Compliance       |rdar://141270725 |
```

#### 状态值说明：
- **`testable`** - 可测试状态（显示为绿色 TESTABLE）
- **`issue`** - 有问题但可测试（显示为黄色 TESTABLE）
- **`notready`** - 未就绪（显示为红色 NOT READY）
- **`na`** - 不适用（显示为灰色 N/A）
- **`blocked`** - 被阻塞（显示为红色 BLOCKED）

### 2. Executive Summary 部分

执行摘要部分使用无序列表格式，支持多级缩进。

#### 格式示例：
```markdown
# Executive Summary
- SiVal board with Linux (Linux_release_20241203)
    - Updated TnT FW to v1.34
    - Feature-Sweep, in progress (90%), ETC 1/14
- Dev board with iOS (Luck23A148a)
    - IOT test, in progress (90%)
        - P0/P1 completed
        - P2/P3 in progress (80%), ETC 1/31
- Major Issue
    - [HIGH] Multiple CTS failed with "Source DUT does not transmit video stream"
        - Video is stopped immediately after video started by upper layer
        - rdar://141270725 (CTS video transmission failure)
```

#### 缩进规则：
- 使用 **Tab键** 或 **4个空格** 表示一级缩进
- 支持多级嵌套缩进（最多支持4级）
- 每个缩进级别会在HTML中生成相应的嵌套列表
- 可以在任意级别使用状态标签

### 3. Radar List 部分

这是最复杂的部分，用于生成状态表格，包含区域、状态和摘要信息。

#### 基本结构：
```markdown
# Radar List

### USB-IOT
- [HIGH] [up] 主要问题描述
    - 详细问题说明
        - 具体技术细节
        - 影响范围说明
- [MEDIUM] 次要问题
    - 问题详细描述
    - rdar://132552351 (ASMedia chipset enumeration)

### Compliance
- [LOW] [down] 合规性测试状态
    - 测试进展情况
    - 遇到的问题和解决方案
```

### 状态标签详细说明

#### 状态值定义：
- **`[HIGH]`** - 高风险/高优先级（红色显示）
- **`[MEDIUM]`** - 中等风险/中等优先级（黄色显示）
- **`[LOW]`** - 低风险/低优先级（绿色显示）
- **`[MED-HIGH]`** - 中高风险（橙色显示）
- **`[NOT STARTED]`** - 未开始（灰色显示）

#### 方向指示符：
- **`[up]`** - 上升趋势（↗ 箭头）
- **`[down]`** - 下降趋势（↘ 箭头）
- **无指示符** - 平稳状态

#### 使用规则：
1. 状态标签可以出现在任意列表项中
2. 方向指示符只能在Radar List部分的主状态行使用
3. 状态值不区分大小写，但建议使用大写
4. 状态标签会自动应用对应的颜色样式

### rdar链接格式支持

#### 支持的两种格式：

**新格式（推荐）：**
```markdown
- rdar://146598443 (Storage enumeration issue)
- rdar://152635214 (Keyboard enumeration problem)
```

**旧格式（兼容性）：**
```markdown
- Storage issue (rdar://146598443) needs attention
- Check this bug [rdar://152635214] for details
```

#### 转换结果：
- **新格式** → `Storage enumeration issue (rdar://146598443)`
- **旧格式** → 保持原有格式，添加链接功能

#### 注意事项：
- 两种格式可以在同一文件中混合使用
- 所有rdar链接都会自动转换为可点击的超链接
- 链接样式保持一致，使用相同的CSS类

## ⚙️ 配置文件说明 (config.json)

### 配置文件结构

```json
{
  "status_mappings": {
    "HIGH": {"css_class": "sh", "priority": 1},
    "MEDIUM": {"css_class": "sm", "priority": 2},
    "LOW": {"css_class": "sl", "priority": 3},
    "MED-HIGH": {"css_class": "smh", "priority": 4},
    "NOT STARTED": {"css_class": "sn", "priority": 5}
  },
  "feature_status_mappings": {
    "testable": {"css_class": "testable"},
    "ready": {"css_class": "ready"},
    "issue": {"css_class": "issue"},
    "notready": {"css_class": "notready"},
    "na": {"css_class": "na"},
    "blocked": {"css_class": "blocked"}
  }
}
```

### 配置项详解

#### 1. status_mappings（状态标签映射）
用于配置Markdown中状态标签的显示样式：

- **键名**: 状态标签名称（如"HIGH", "MEDIUM"等）
- **css_class**: 对应的CSS类名，控制颜色和样式
- **priority**: 优先级数字，数字越小优先级越高

#### 2. feature_status_mappings（功能状态映射）
用于配置Feature readiness status表格中的状态显示：

- **键名**: 表格中的状态值（如"testable", "issue"等）
- **css_class**: 对应的CSS类名

### 自定义配置

#### 添加新的状态标签：
```json
{
  "status_mappings": {
    "CRITICAL": {"css_class": "sc", "priority": 0},
    "URGENT": {"css_class": "su", "priority": 1}
  }
}
```

#### 修改现有状态样式：
```json
{
  "status_mappings": {
    "HIGH": {"css_class": "custom-high", "priority": 1}
  }
}
```

### CSS样式定制

配合`style.css`文件，可以自定义状态标签的显示效果：

```css
.sc { color: #ff0000; font-weight: bold; }  /* CRITICAL - 红色加粗 */
.su { color: #ff6600; font-weight: bold; }  /* URGENT - 橙色加粗 */
.custom-high { color: #cc0000; }            /* 自定义HIGH样式 */
```

## 🎯 Output Examples

### Status Tag Conversion
**Input**: `[HIGH] Critical security issue`
**Output**: `<span class="sh">[HIGH]</span> Critical security issue`

### rdar Link Conversion
**Input**: `rdar://146598443 (Storage enumeration issue)`
**Output**: `Storage enumeration issue (<a href="rdar://146598443"><span class="s4">rdar://146598443</span></a>)`

### Multi-level List Conversion
**Input**:
```markdown
- [HIGH] Main issue
    - [MEDIUM] Sub-issue
        - Detail item
```

**Output**:
```html
<ul>
<li class="level-0"><span class="sh">[HIGH]</span> Main issue</li>
<ul>
<li class="level-1"><span class="sm">[MEDIUM]</span> Sub-issue</li>
<ul>
<li class="level-2">Detail item</li>
</ul>
</ul>
</ul>
```

## 🔧 Requirements

- **Python 2.7+ or 3.x** (both versions supported)
- **No external dependencies** - uses only standard library
- **Cross-platform** - works on Windows, macOS, and Linux

## 📁 Project Structure

```
status-generator/
├── status_generator_minimal.py      # Lightweight version
├── status_generator_refactored.py   # Full-featured version
├── config.json                      # Configuration file
├── style.css                        # Default CSS styles
├── report.md                        # Example input file
├── report.html                      # Example output file
├── Achieved/                        # Legacy versions
│   ├── status_generator.py         # Original implementation
│   └── Readme.txt                  # Legacy documentation
└── README.md                       # This file
```

## 🚀 Performance

- **Processing Speed**: >4M characters/second
- **Memory Efficient**: Minimal memory footprint
- **Optimized Regex**: Pre-compiled patterns for maximum performance
- **Scalable**: Handles large documents (1000+ lines) efficiently

## 🔄 Migration Guide

### From Legacy Format
If you have existing files with the old rdar format `title (rdar://123)`, no changes needed - both formats are supported simultaneously.

### From Original Version
1. Replace `status_generator.py` with `status_generator_minimal.py`
2. Update command line usage (see examples above)
3. Verify output formatting meets requirements

## 🤝 Contributing

This tool is designed to be simple, fast, and reliable. When contributing:
1. Maintain backward compatibility
2. Add tests for new features
3. Update documentation
4. Follow existing code style

## 📄 License

Open source - feel free to modify and distribute according to your needs.
