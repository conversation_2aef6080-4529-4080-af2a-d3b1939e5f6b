<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Status Report</title>
    <style>
/* styles.css */
p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c; min-height: 12.0px}
p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 14.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c; min-height: 16.0px}
p.p3 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 10.0px 'Helvetica Neue'; color: #ffffff; -webkit-text-stroke: #ffffff}
p.p4 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px 'Helvetica Neue'; color: #032553; -webkit-text-stroke: #032553}
p.p5 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 10.5px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #000000}
p.p6 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c; min-height: 16.0px}
p.p7 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: left; font: 12.0px 'Helvetica Neue'; color: #032553; -webkit-text-stroke: #032553; background-color: #ffffff}
p.p8 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 10.0px 'Helvetica Neue'; color: #ffffff}
p.p9 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: left; font: 12.0px 'Helvetica Neue'; color: #000000}

li.level-0 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
li.level-1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
li.level-2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
li.level-3 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
li.level-4 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}    
li.level-7 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #000000}
span.s1 {background-color: #ffffff; -webkit-text-stroke: 0px #000000}
span.s2 {font-kerning: none}
span.s4 {font-kerning: none; color: #0a59bd; -webkit-text-stroke: 0px #0a59bd}
span.sl {background-color: rgb(56,132,93);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 30px}
span.smh {background-color: rgb(250,124,13);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 14px;}
span.sn {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 8px;}
span.sm {background-color: rgb(247,247,0);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
span.sh {background-color: rgb(177,56,22);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 29px;}
span.ta {background-color: rgb(56,132,93);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
span.tawi {background-color: rgb(247,247,0);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
span.nr {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 16px}
span.bl {background-color: rgb(177,56,22);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
span.na {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 37px}
span.sna {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 34px}

table.t1 {border-collapse: collapse; table-layout: fixed}
td.td1 {vertical-align: middle; width: 125.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td2 {vertical-align: middle; width: 125.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td3 {vertical-align: middle; width: 858.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td4 {vertical-align: middle; width: 125.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td5 {vertical-align: middle; width: 125.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td6 {vertical-align: middle; width: 858.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td7 {vertical-align: middle; width: 136.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td8 {vertical-align: middle; width: 572.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td10 {vertical-align: middle; width: 572.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
td.td11 {vertical-align: right; width: 175px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
td.td12 {vertical-align: middle; width: 125px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
td.td13 {vertical-align: middle; width: 150px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
td.td14 {vertical-align: middle; width: 558px; text-align: left; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}

th.th11 {vertical-align: middle; width: 50px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
th.th12 {vertical-align: middle; width: 125px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
th.th13 {vertical-align: middle; width: 125px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
th.th14 {vertical-align: middle; width: 858px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}

</style>
</head>
<body>
<p class="p1"><span class="s2"><b>Executive Summary</b></span></p>
<ul class="ul1">
<li class="level-0"><span class="s1">Feature readiness status</span></li></ul>
<table cellpadding="0" cellspacing="0" class="t1">
<thead>
<tr>
<th class="th11"><p class="p5"><span class="s1"><b>Sival</b></span></p></th>
<th class="th12"><p class="p5"><span class="s1"><b>Dev</b></span></p></th>
<th class="th13"><p class="p4"><span class="s1"><b>Feature</b></span></p></th>
<th class="th14"><p class="p4"><span class="s1"><b>Notes</b></span></p></th>
</tr>
</thead>
<tbody>
<tr>
<td class="td11"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td12"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td13"><p class="p9"><span class="s1">USB-C Power Delivery</span></p></td>
<td class="td14"><p class="p9"><span class="s1"></span></p></td>
</tr>
<tr>
<td class="td11"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td12"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td13"><p class="p9"><span class="s1">DisplayPort Alt Mode</span></p></td>
<td class="td14"><p class="p9"><span class="s1"></span></p></td>
</tr>
<tr>
<td class="td11"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td12"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td13"><p class="p9"><span class="s1">USB 3.2 Gen 2</span></p></td>
<td class="td14"><p class="p9"><span class="s1"></span></p></td>
</tr>
<tr>
<td class="td11"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td12"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td13"><p class="p9"><span class="s1">Thunderbolt 4</span></p></td>
<td class="td14"><p class="p9"><span class="s1"></span></p></td>
</tr>
<tr>
<td class="td11"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td12"><p class="p8"><span class="ta"><b>TESTABLE</b></span></p></td>
<td class="td13"><p class="p9"><span class="s1">Power Management</span></p></td>
<td class="td14"><p class="p9"><span class="s1"></span></p></td>
</tr>
</tbody>
</table>

<ul>
<li class="level-0">All core features have been tested and are stable.</li>
</ul>
<ul>
<li class="level-0">No critical issues identified during recent testing cycles.</li>
</ul>
<ul>
<li class="level-0">Performance metrics are within expected parameters.</li>
</ul>
<p class="p7"><span class="s1"><b>Radar List</b></span></p>
<table cellpadding="0" cellspacing="0" class="t1">
<thead>
<tr>
<td class="td1" valign="top"><p class="p2"><span class="s2"><b>Area</b></span></p></td>
<td class="td2" valign="top"><p class="p2"><span class="s2"><b>Status</b></span></p></td>
<td class="td3" valign="top"><p class="p2"><span class="s2"><b>Summary</b></span></p></td>
</tr>
</thead>
<tbody>
<tr>
<td class="td4" valign="top"><p class="p6"><span class="s2"><b>USB-C Functionality</b></span></p></td>
<td class="td5" valign="top">[LOW]</td>
<td class="td6" valign="top"><ul><li class="level-1">All USB-C features are stable and performing as expected.</li><li class="level-1">Minor optimizations are being explored for future updates.</li></ul></td>
</tr>
<tr>
<td class="td4" valign="top"><p class="p6"><span class="s2"><b>Display Output</b></span></p></td>
<td class="td5" valign="top">[LOW]</td>
<td class="td6" valign="top"><ul><li class="level-1">DisplayPort Alt Mode is fully functional.</li><li class="level-1">Compatibility with various monitors confirmed.</li></ul></td>
</tr>
<tr>
<td class="td4" valign="top"><p class="p6"><span class="s2"><b>System Performance</b></span></p></td>
<td class="td5" valign="top">[LOW]</td>
<td class="td6" valign="top"><ul><li class="level-1">Overall system performance is good.</li><li class="level-1">No significant regressions detected.</li></ul></td>
</tr>
</tbody>
</table>

<p class="p1"><br/></p>
<p class="p1"><br/></p>
<table cellpadding="0" cellspacing="0" class="t1">
<tbody>
<tr>
<td class="td7" valign="top">
<p class="p2"><span class="s2"><b>Quality Legend</b></span></p>
</td>
<td class="td8" valign="top">
<p class="p4"><span class="s2"><b>Definition</b></span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sl"><b>LOW</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">No anticipated risks to meet objectives</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sm"><b>MEDIUM</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Minor none-silicon issues impacting customer quality</span><span class="s1"><br/>
</span><span class="s2">Minor testing blocked from completion</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="smh"><b>MED-HIGH</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Silicon issues currently in the milestone and on track to be fixed by TO date</span><span class="s1"><br/>
</span><span class="s2">Critical none-silicon issues impacting customer quality</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sh"><b>HIGH</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Critical Silicon issues without path to closure by TO date</span><span class="s1"><br/>
</span><span class="s2">Critical Silicon issues not screened or not in the milestone (deferred or otherwise)</span><span class="s1"><br/>
</span><span class="s2">Significant testing blocked from completion</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sn"><b>NOT STARTED</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Test have not started yet</span></p>
</td>
</tr>
</tbody>
</table>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
</body>
</html>