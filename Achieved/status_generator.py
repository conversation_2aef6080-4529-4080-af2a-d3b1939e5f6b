import re
from bs4 import BeautifulSoup
import markdown
import os
os.chdir(os.path.dirname(os.path.abspath(__file__)))

def get_indent_level(line):
    match = re.match(r"^(\t+)", line)
    if match:
        return len(match.group(1)) 
    else:
        return 0

def process_line(line, indent_level):
  indicator = ''
  if re.search(r'\[up\](.*)',line):
    indicator = 'up'
    line = line.replace('[up]','')
  elif re.search(r'\[down\](.*)',line):
    indicator = 'down'
    line = line.replace('[down]','')
  if line.upper().replace('- ', '', 1) in status_dict:
    match line.upper().replace('- ', '', 1):
      case 'LOW':
        if indicator == 'up':
          line = f'<br/><p class="p1">&#8599;<span class="sl"><b>LOW</b></span></p></li></ul>\n'
        elif indicator == 'down':
          line = f'<br/><p class="p1">&#8600;<span class="sl"><b>LOW</b></span></p></li></ul>\n'
        else:
          #line = f'<br/><p class="p1">&#8594;<span class="sl"><b>LOW</b></span></p></li></ul>\n'
          line = f'<br/><p class="p1"><span class="sl"><b>LOW</b></span></p></li></ul>\n'
      case 'MEDIUM':
        if indicator == 'up':
          line = f'<br/><p class="p1">&#8599;<span class="sm"><b>MEDIUM</b></span></p></li></ul>\n'
        elif indicator == 'down':
          line = f'<br/><p class="p1">&#8600;<span class="sm"><b>MEDIUM</b></span></p></li></ul>\n'
        else:
          # line = f'<br/><p class="p1">&#8594;<span class="sm"><b>MEDIUM</b></span></p></li></ul>\n'
          line = f'<br/><p class="p1"><span class="sm"><b>MEDIUM</b></span></p></li></ul>\n'          
      case 'MED-HIGH':
        if indicator == 'up':
          line = f'<br/><p class="p1">&#8599;<span class="smh"><b>MED-HIGH</b></span></p></li></ul>\n'
        elif indicator == 'down':
          line = f'<br/><p class="p1">&#8600;<span class="smh"><b>MED-HIGH</b></span></p></li></ul>\n'
        else:
          # line = f'<br/><p class="p1">&#8594;<span class="smh"><b>MED-HIGH</b></span></p></li></ul>\n'
          line = f'<br/><p class="p1"><span class="smh"><b>MED-HIGH</b></span></p></li></ul>\n'
      case 'HIGH':
        if indicator == 'up':
          line = f'<br/><p class="p1">↗&#8599;<span class="sh"><b>HIGH</b></span></p></li></ul>\n'
        elif indicator == 'down':
          line = f'<br/><p class="p1">&#8600;<span class="sh"><b>HIGH</b></span></p></li></ul>\n'
        else:
          # line = f'<br/><p class="p1">&#8594;<span class="sh"><b>HIGH</b></span></p></li></ul>\n'
          line = f'<br/><p class="p1"><span class="sh"><b>HIGH</b></span></p></li></ul>\n'
      case 'NOT STARTED':
        line = f'<br/><p class="p1"><span class="sn"><b>NOT STARTED</b></span></p></li></ul>\n'
  else:
    line = f'<li class="level-{indent_level}">{line.strip().replace("- ", "", 1)}</li>'
  return line

def seprate_sections(mark, list):
    sections = []
    curr_section = ""
    for line in list:
        if line.startswith(mark):
            if curr_section:
                sections.append(curr_section.strip())
            curr_section = line
        else:
            curr_section += line
    if curr_section:
        sections.append(curr_section.strip())
    return sections

def process_feature(line):
  if line.lower() in feature_dict:
    match line.lower():
        case 'ta':
          line = f'<p class="p8"><span class="ta"><b>TESTABLE</b></span></p>'
        case 'tawi':
          line = f'<p class="p8"><span class="tawi"><b>TESTABLE</b></span></p>'
        case 'nr':
          line = f'<p class="p8"><span class="nr"><b>NOT READY</b></span></p>'
        case 'bl':
          line = f'<p class="p8"><span class="bl"><b>BLOCKED</b></span></p>'
        case 'na':
          line = f'<p class="p8"><span class="na"><b>N/A</b></span></p>'
  elif re.sub(pattern,r'<a href=\1><span class=s4>(\1)</span></a>',line):
    line = re.sub(pattern,r'<a href=\1><span class=s4>(\1)</span></a>',line)
    line = re.sub(r'\[new\]','🆕',line)
  return line

# read in the input file
with open("report.md", "r") as f:
    markdown = f.readlines()


# split the file contents by the "##" marker
sections = seprate_sections("##",markdown)
status_dict = ['LOW','MEDIUM','MED-HIGH','HIGH','NOT STARTED','N/A']
feature_dict = ['ta','tawi','bl','nr','na']

summary_html = "<ul>\n"
current_indent_level = 0
summary_section = sections[1].split("\n")
for line in summary_section[1:]:
    indent_level = get_indent_level(line)
    if indent_level > current_indent_level:
        # 当前行的层级比上一行高，需要增加一个嵌套的bullet point
        summary_html += "<ul>\n" * (indent_level - current_indent_level)
        current_indent_level = indent_level
    elif indent_level < current_indent_level:
        # 当前行的层级比上一行低，需要结束之前的bullet point嵌套
        summary_html += "</ul>\n" * (current_indent_level - indent_level)
        current_indent_level = indent_level

    summary_html += process_line(line, indent_level)

# 结束所有的bullet point嵌套
summary_html += "</ul>\n" * current_indent_level
pattern = r'\[(rdar.*?)\]'
summary_html = re.sub(pattern,r'<a href=\1><span class=s4>(\1)</span></a>',summary_html)
summary_html = re.sub(r'\[new\]','🆕',summary_html)

# Merge the content with an HTML file
with open("input.html") as fp:
    
  soup = BeautifulSoup(fp, "html.parser")
  example_div_sm = soup.find("div", {"id": 'Summary'})
  code_soup_sm = BeautifulSoup(summary_html,features="html.parser")
  example_div_sm.append(code_soup_sm)
  # print(summary_html)

feature_html = "<ul>\n"
feature_section = sections[0].split("\n")
for feature_lines in feature_section[1:]:
  feature_lines = feature_lines.split(';')
  sival_feature_status = process_feature(feature_lines[0])
  dev_feature_status = process_feature(feature_lines[1])
  feature_cat = feature_lines[2]
  if feature_lines[3]:
    radar = process_feature(feature_lines[3])
    radar = '<p class="p9"><span class="s1">' + feature_cat + " - " + radar
  else:
     radar = '<p class="p9"><span class="s1">' + feature_cat

  example_div_feature_sival = soup.find("div", {"id": feature_cat + ' sival'})
  code_soup_sival_feature_status = BeautifulSoup(sival_feature_status,features="html.parser")
  example_div_feature_sival.append(code_soup_sival_feature_status)
  example_div_feature_dev = soup.find("div", {"id": feature_cat + ' dev'})
  code_soup_dev_feature_status = BeautifulSoup(dev_feature_status,features="html.parser")
  example_div_feature_dev.append(code_soup_dev_feature_status)
  example_div_feature_radar = soup.find("div", {"id": feature_cat + ' radar'})
  code_soup_feature_radar = BeautifulSoup(radar,features="html.parser")
  example_div_feature_radar.append(code_soup_feature_radar)

# print(sections)
table_sections = sections[3:]

for contents in table_sections:
  content = contents.split("\n")
  # print(content)
  table_html = "<ul>\n"
  if re.search(r'\[up\](.*)',content[1]):
    status = re.search(r'\[up\](.*)',content[1]).group(1)
  elif re.search(r'\[down\](.*)',content[1]):
    status = re.search(r'\[down\](.*)',content[1]).group(1)
  else:
    status = content[1].upper().replace('- ', '', 1)
  match status.upper():
    case 'LOW':
      if re.search(r'- \[up\](.*)',content[1]):
        status_full = '<p class="p2">&#8599;<span class="sl"><b>LOW</b></span></p>'
      else:
        if re.search(r'- \[down\](.*)',content[1]):
          status_full = '<p class="p2">&#8600;<span class="sl"><b>LOW</b></span></p>'
        else:
          status_full = '<p class="p2">&#8594;<span class="sl"><b>LOW</b></span></p>'
    case 'MEDIUM':
      if re.search(r'\[up\](.*)',content[1]):
        status_full = '<p class="p2">&#8599;<span class="sm"><b>MEDIUM</b></span></p>'
      else:
        if re.search(r'\[down\](.*)',content[1]):
          status_full = '<p class="p2">&#8600;<span class="sm"><b>MEDIUM</b></span></p>'
        else:
          status_full = '<p class="p2">&#8594;<span class="sm"><b>MEDIUM</b></span></p>'
    case 'MED-HIGH':
      if re.search(r'\[up\](.*)',content[1]):
        status_full = '<p class="p2">&#8599;<span class="smh"><b>MED-HIGH</b></span></p>'
      else:
        if re.search(r'\[down\](.*)',content[1]):
          status_full = '<p class="p2">&#8600;<span class="smh"><b>MED-HIGH</b></span></p>'
        else:
          status_full = '<p class="p2">&#8594;<span class="smh"><b>MED-HIGH</b></span></p>'      
    case 'HIGH':
      if re.search(r'\[up\](.*)',content[1]):
        status_full = '<p class="p2">&#8599;<span class="sh"><b>HIGH</b></span></p>'
      else:
        if re.search(r'\[down\](.*)',content[1]):
          status_full = '<p class="p2">&#8600;<span class="sh"><b>HIGH</b></span></p>'
        else:
          status_full = '<p class="p2">&#8594;<span class="sh"><b>HIGH</b></span></p>'
    case 'NOT STARTED':
      status_full = '<p class="p2"><span class="sn"><b>NOT STARTED</b></span></p>'
    case 'N/A':
      status_full = '<p class="p2"><span class="sna"><b>N/A</b></span></p>'

  heading = content[0].replace("### ","", 1)
  example_div_s = soup.find("div", {"id": heading + ' status'})
  code_soup_s = BeautifulSoup(status_full,features="html.parser")
  example_div_s.clear()
  example_div_s.append(code_soup_s)
  
  table_html = "<ul>\n"
  current_indent_level = 1
  for line in content[2:]:
    indent_level = get_indent_level(line)
    if indent_level > current_indent_level:
        # 当前行的层级比上一行高，需要增加一个嵌套的bullet point
        table_html += "<ul>\n" * (indent_level - current_indent_level)
        current_indent_level = indent_level
    elif indent_level < current_indent_level:
        # 当前行的层级比上一行低，需要结束之前的bullet point嵌套
        table_html += "</ul>\n" * (current_indent_level - indent_level)
        current_indent_level = indent_level
    table_html += process_line(line, indent_level)

  # 结束所有的bullet point嵌套
  table_html += "</ul>\n" * current_indent_level
  table_html = table_html.replace("<ul>\n</ul>\n","",1)

  pattern = r'\[(rdar.*?)\]'
  table_html = re.sub(pattern,r'<a href=\1><span class=s4>(\1)</span></a>',table_html)
  table_html = re.sub(r'\[new\]','🆕',table_html)

      
  example_div = soup.find("div", {"id": heading})
  code_soup = BeautifulSoup(table_html,features="html.parser")
  example_div.clear()
  example_div.append(code_soup)


with open("output.html", "w", encoding="utf-8") as file:
    file.write(str(soup))