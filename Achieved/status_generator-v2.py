import re
import markdown
from bs4 import BeautifulSoup
import webbrowser
import argparse

STATUS_DICT = {
    'LOW': 'LOW',
    'MEDIUM': 'MEDIUM', 
    'MED-HIGH': 'MED-HIGH',
    'HIGH': 'HIGH',
    'NOT STARTED': 'NOT STARTED'
}

def get_indent_level(line):
    """
    Returns the indentation level based on leading tabs or spaces.
    Treats 1 tab or 4 spaces as one indent level.
    """
    count = 0
    i = 0
    while i < len(line):
        if line[i] == '\t':
            count += 1
            i += 1
        elif line[i:i+4] == '    ':
            count += 1
            i += 4
        elif line[i] == ' ':
            # Count consecutive spaces less than 4 as partial indent
            space_count = 0
            while i < len(line) and line[i] == ' ':
                space_count += 1
                i += 1
            count += space_count // 4
        else:
            break
    return count

def process_table_section(md_text):
    lines = md_text.splitlines()
    table_lines = []
    area = None # Area name
    status = None # Status 
    summary_items = [] # Summary items
    current_indent_level = 0 # Current indent level
    expecting_status = False # Expecting status

    for line in lines:
        # Check if the line is an Area header (###)
        header_match = re.match(r'###\s+(.+)', line)
        if header_match:
            if area and summary_items:
                summary_html = "".join(summary_items) + "</ul>" * current_indent_level
                table_lines.append(f"| {area} | {status} | {summary_html} |")
                summary_items = []
                current_indent_level = 0
            
            area = header_match.group(1)
            status = None
            expecting_status = True
            continue

        bullet_match = re.match(r'^(\s*)-\s*(.*)', line)
        if bullet_match:
            indent = get_indent_level(line)
            text = bullet_match.group(2).strip()

            if expecting_status:
                # Replace status with corresponding status class and arrow if available
                # Remove [up]/[down] indicators from text before getting status
                clean_text = text.replace('[up]', '').replace('[down]', '').strip()
                status = STATUS_DICT.get(clean_text, clean_text).upper()  # Replace with descriptive status if available
                span_class = {
                    'LOW': 'sl', 'MEDIUM': 'sm', 'MED-HIGH': 'smh',
                    'HIGH': 'sh', 'NOT STARTED': 'sn'
                }.get(status, '')

                # Add arrow direction if mentioned
                indicator = "[down]" if "[down]" in text.lower() else "[up]" if "[up]" in text.lower() else ""
                arrow = {'[up]': '&#8599;', '[down]': '&#8600;', '': '&#8594;'}.get(indicator, '&#8594;')
                status = f'<p class="p1">{arrow}<span class="{span_class}"><b>{status}</b></span></p>'
                
                expecting_status = False
            else:
                if indent > current_indent_level:
                    summary_items.append("<ul>" * (indent - current_indent_level))
                elif indent < current_indent_level:
                    summary_items.append("</ul>" * (current_indent_level - indent))
                
                # Check if summary text matches any status and format accordingly
                status_match = None
                status_pattern = r'\[(LOW|MEDIUM|MED-HIGH|HIGH|NOT STARTED)\]'
                match = re.search(status_pattern, text.upper())
                if match:
                    status_match = match.group(1)

                if status_match:
                    span_class = {
                        'LOW': 'sl', 'MEDIUM': 'sm', 'MED-HIGH': 'smh',
                        'HIGH': 'sh', 'NOT STARTED': 'sn'
                    }.get(status_match, '')
                    text = text.replace(f'[{status_match}]', "").strip()
                    summary_items.append(f'<p class="p1"><span class="{span_class}"><b>{status_match}</b></span></p>{text}')
                else:
                    summary_items.append(f'<li class="level-{indent}">{text}</li>')
                current_indent_level = indent

    if area and summary_items:
        summary_html = "".join(summary_items) + "</ul>" * current_indent_level
        table_lines.append(f"| {area} | {status} | {summary_html} |")

    header = "| Area | Status | Summary |\n| --- | --- | --- |"
    table_content = header + "\n" + "\n".join(table_lines)
    return table_content

def split_sections(md_text):
    # Split the markdown into sections by top-level headers (#)
    sections = re.split(r'(?m)^# ', md_text)
    
    # Dictionary to hold each section
    section_dict = {}
    
    for section in sections:
        if not section.strip():
            continue
        lines = section.splitlines()
        title = lines[0].strip()
        content = "\n".join(lines[1:]).strip()
        section_dict[title] = content
    
    return section_dict

def convert_markdown(md_text):
    # Step 1: Split the markdown text into sections
    sections = split_sections(md_text)
    
    # Step 2: Process each section as needed
    output = []
    
    for title, content in sections.items():
        if title == "Radar List":
            # Process radar list table
            table_content = process_table_section(content)
            output.append("# " + title + "\n\n" + table_content)
        elif title == "Feature readiness status":
            # Preserve the original content for Feature readiness status
            output.append("# " + title + "\n" + content)
        else:
            # For other sections, process Executive Summary differently
            lines = content.splitlines()
            processed_lines = []

            current_ul = None
            current_level = -1
            
            for line in lines:
                if line.strip().startswith('-'):
                    indent = get_indent_level(line)
                    indent_level = indent
                    text_content = line.strip()[2:].strip()
                    
                    # Close deeper nested lists if going back up levels
                    while current_level >= 0 and indent_level <= current_level:
                        processed_lines.append('</ul>')
                        current_level -= 1
                    
                    # Start new list if needed
                    if current_level < indent_level:
                        processed_lines.append('<ul>')
                        current_level = indent_level
                        
                    processed_lines.append(f'<li class="level-{indent_level}">{text_content}</li>')
                else:
                    # Close all open lists when hitting non-list content
                    while current_level >= 0:
                        processed_lines.append('</ul>')
                        current_level -= 1
                    processed_lines.append(line)

            # Close any remaining open lists at the end
            while current_level >= 0:
                processed_lines.append('</ul>')
                current_level -= 1
                
            output.append("# " + title + "\n\n" + "\n".join(processed_lines))
    
    # Join all processed sections back into the final markdown output
    return "\n\n".join(output)

def convert_rdar_to_links(text):
        pattern = r'[\[\(](rdar://[^\]\)]+?)[\]\)]'
        def replace_link(match):
            rdar = match.group(1)
            return f'<a href="{rdar}"><span class="s4">({rdar})</span></a>'
        return re.sub(pattern, replace_link, text)
    

def markdown_to_html_with_tags(md_file_path, html_file_path, css_file_path=None):
    # Read the markdown file
    with open(md_file_path, 'r', encoding='utf-8') as md_file:
        md_text = md_file.read()

    md_text = convert_markdown(md_text)
    # Convert special tag syntax [high]text[/high] into <span class="sh">text</span>
    md_text = re.sub(r'\[HIGH\]', r'<p class="p2"><span class="sh"><b>HIGH</b></span></p>', md_text)


    # Convert markdown to HTML
    html_content = markdown.markdown(md_text, extensions=['tables'])
    
   
    # Parse the modified HTML content with BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Add custom CSS if provided
    if css_file_path:
        with open(css_file_path, 'r', encoding='utf-8') as css_file:
            css_content = css_file.read()
        css_html = f"<style>\n{css_content}\n</style>"
    else:
        css_html = ""
    
    # Split the HTML content into separate <div> blocks based on <h1> headers
    divs = []
    current_div = None
    for tag in soup.find_all(['h1']):
        if tag.name == 'h1':
            # Start a new div for each new section, but remove <h1> tags from output
            if current_div:
                divs.append(str(current_div))
            current_div = soup.new_tag('div', **{'class': 'section'})
            current_div.append(soup.new_tag("p", string=tag.get_text()))  # Add the <h1> text as a <p> instead of <h1>
        else:
            current_div.append(str(tag))  # Add other elements (p, table) to the current div
    
    # Append the last div if any
    if current_div:
        divs.append(str(current_div))

    # Target each table individually by index or specific position
    # Example: Modify the first table
    first_table = soup.find_all('table')[0]
    first_table['cellpadding'] = "0"
    first_table['cellspacing'] = "0"
    first_table['class'] = first_table.get('class', []) + ['t1']

    # Modify the header row of the first table (assuming it exists)
    rows = first_table.find_all('tr')
    header_row = rows[0]
    for index, cell in enumerate(header_row.find_all(['th', 'td'])):
        if index == 0 or index == 1:
            span = soup.new_tag('span', **{'class': 's1'})
            p = soup.new_tag('p', **{'class': 'p5'})
            bold_tag = soup.new_tag("b")
            bold_tag.string = cell.get_text()
            cell.clear()            
            span.append(bold_tag)
            p.append(span)
            cell.append(p)
        elif index == 2 or index == 3:  
            span = soup.new_tag('span', **{'class': 's1'})
            span.string = cell.get_text()
            cell.clear()  
            cell.append(span)
            p = soup.new_tag('p', **{'class': 'p4'})
            p.append(span)
            cell.append(p)

    for row in rows[1:]:
        for index, cell in enumerate(row.find_all(['th', 'td'], recursive=False)):
            if index == 0 or index == 1:
                p = soup.new_tag('p', **{'class': 'p8'})
                text = cell.get_text().strip().lower()
                span = soup.new_tag('span')
                match text:
                    case 'testable':
                        span['class'] = 'ta'
                        cell.string = 'TESTABLE'
                    case 'issue':
                        span['class'] = 'tawi'
                        cell.string = 'TESTABLE'
                    case 'notready':
                        span['class'] = 'nr'
                        cell.string = 'NOT READY'
                    case 'na':
                        span['class'] = 'na'
                        cell.string = 'N/A'
                    case 'blocked':
                        span['class'] = 'bl'
                        cell.string = 'BLOCKED'
                bold = soup.new_tag('b')
                bold.string = cell.get_text()
                span.append(bold)
                cell.clear()
                p.append(span)
                cell.append(p)
            elif index == 2 or index == 3:
                span = soup.new_tag('span', **{'class': 's1'})
                span.string = cell.get_text()
                cell.clear()
                p = soup.new_tag('p', **{'class': 'p9'})
                p.append(span)
                cell.append(p)

    for row in rows:
        header_cells = row.find_all('th')
        if header_cells:
            header_cells[0]['class']=['th11']
            header_cells[1]['class']=['th12'] 
            header_cells[2]['class']=['th13']
            header_cells[3]['class']=['th14']
        cells = row.find_all('td')
        if cells:
            cells[0]['class']=['td11']
            cells[1]['class']=['td12']
            cells[2]['class']=['td13']
            cells[3]['class']=['td14']

    
    # Example: Modify the second table with a different style
    second_table = soup.find_all('table')[1]
    second_table['cellpadding'] = "0"
    second_table['cellspacing'] = "0"
    second_table['class'] = second_table.get('class', []) + ['t1']

    # Modify the header row of the second table
    second_header_row = second_table.find('tr')
    if second_header_row:
        for index, header_cell in enumerate(second_header_row.find_all(['th', 'td'], recursive=False)):
            header_cell.name = 'td'
            # Set class to td1, td2, td3 for the first row headers
            header_cell['class'] = [f'td{index + 1}']
            header_cell['valign'] = 'top'
            header_text = header_cell.get_text()
            header_cell.clear()
            
            p_tag = soup.new_tag("p", **{"class": "p2"})
            span_tag = soup.new_tag("span", **{"class": "s2"})
            bold_tag = soup.new_tag("b")
            bold_tag.string = header_text
            span_tag.append(bold_tag)
            p_tag.append(span_tag)
            header_cell.append(p_tag)

    # For data rows, update classes to td4, td5, td6
    data_rows = second_table.find_all('tr')[1:]
    for row in data_rows:
        for index, cell in enumerate(row.find_all(['td'])):
            cell['class'] = [f'td{index + 4}']
            cell['valign'] = 'top'
            

            # Create appropriate p and span tags based on column
            if index == 0:
                p_tag = soup.new_tag("p", **{"class": "p6"})
                span_tag = soup.new_tag("span", **{"class": "s2"})
                bold_tag = soup.new_tag("b")
                bold_tag.string = cell.get_text()
                span_tag.append(bold_tag)
                cell.clear()
                p_tag.append(span_tag)
                cell.append(p_tag)
            
    # Convert rdar links to hyperlinks after all other HTML modifications
    final_html = str(soup)
    final_html = convert_rdar_to_links(final_html)
    # Replace Executive Summary header with styled version
    final_html = final_html.replace('<h1>Executive Summary</h1>', '')
    final_html = final_html.replace('<h1>Feature readiness status</h1>', '''<p class="p1"><span class="s2"><b>Executive Summary</b></span></p>
<ul class="ul1">
<li class="level-0"><span class="s1">Feature readiness status</span></li></ul>''')
    final_html = final_html.replace('<h1>Radar List</h1>', '<p class="p7"><span class="s1"><b>Radar List</b></span></p>')
    

    
    # Wrap the modified content in HTML tags
    full_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Styled Table Report</title>
    {css_html}
</head>
<body>
{final_html}
<p class="p1"><br/></p>
<p class="p1"><br/></p>
<table cellpadding="0" cellspacing="0" class="t1">
<tbody>
<tr>
<td class="td7" valign="top">
<p class="p2"><span class="s2"><b>Quality Legend</b></span></p>
</td>
<td class="td8" valign="top">
<p class="p4"><span class="s2"><b>Definition</b></span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sl"><b>LOW</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">No anticipated risks to meet objectives</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sm"><b>MEDIUM</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Minor none-silicon issues impacting customer quality</span><span class="s1"><br/>
</span><span class="s2">Minor testing blocked from completion</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="smh"><b>MED-HIGH</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Silicon issues currently in the milestone and on track to be fixed by TO date</span><span class="s1"><br/>
</span><span class="s2">Critical none-silicon issues impacting customer quality</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sh"><b>HIGH</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Critical Silicon issues without path to closure by TO date</span><span class="s1"><br/>
</span><span class="s2">Critical Silicon issues not screened or not in the milestone (deferred or otherwise)</span><span class="s1"><br/>
</span><span class="s2">Significant testing blocked from completion</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sn"><b>NOT STARTED</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Test have not started yet</span></p>
</td>
</tr>
</tbody>
</table>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
</body>
</html>"""

    
    # print(full_html)

    # Write to HTML file
    with open(html_file_path, 'w', encoding='utf-8') as html_file:
        html_file.write(full_html)

    # print(f"Markdown file converted to HTML with tags: {html_file_path}")
    html_file_path = f'file://{html_file_path}'
    webbrowser.open(html_file_path)

# Usage
if __name__ == "__main__":
    import os
    parser = argparse.ArgumentParser(description="Convert a markdown report to HTML with tags.")
    parser.add_argument('--md', type=str, default=None, help='Path to the markdown file (default: report.md in script directory)')
    parser.add_argument('--html', type=str, default=None, help='Path to the output HTML file (default: report.html in script directory)')
    parser.add_argument('--css', type=str, default=None, help='Path to the CSS file (default: style.css in script directory)')
    args = parser.parse_args()

    script_dir = os.path.dirname(os.path.abspath(__file__))
    md_file = args.md if args.md else os.path.join(script_dir, 'report.md')
    html_file = args.html if args.html else os.path.join(script_dir, 'report.html')
    css_file = args.css if args.css else os.path.join(script_dir, 'style.css')
    markdown_to_html_with_tags(md_file, html_file, css_file)