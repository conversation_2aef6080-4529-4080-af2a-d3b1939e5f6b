<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title></title>
  <meta name="Generator" content="Cocoa HTML Writer">
  <meta name="CocoaVersion" content="2299">
  <style type="text/css">
    p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c; min-height: 12.0px}
    p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 14.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c; min-height: 16.0px}
    p.p3 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 10.0px 'Helvetica Neue'; color: #ffffff; -webkit-text-stroke: #ffffff}
    p.p4 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px 'Helvetica Neue'; color: #032553; -webkit-text-stroke: #032553}
    p.p5 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 10.5px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #000000}
    p.p6 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c; min-height: 16.0px}
    p.p7 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: left; font: 12.0px 'Helvetica Neue'; color: #032553; -webkit-text-stroke: #032553; background-color: #ffffff}
    p.p8 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: center; font: 10.0px 'Helvetica Neue'; color: #ffffff}
    p.p9 {margin: 0.0px 0.0px 0.0px 0.0px; text-align: left; font: 12.0px 'Helvetica Neue'; color: #000000}

    li.level-0 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
    li.level-1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
    li.level-2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
    li.level-3 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}
    li.level-4 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #121f3c}    
    li.level-7 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px 'Helvetica Neue'; color: #121f3c; -webkit-text-stroke: #000000}
    span.s1 {background-color: #ffffff; -webkit-text-stroke: 0px #000000}
    span.s2 {font-kerning: none}
    span.s4 {font-kerning: none; color: #0a59bd; -webkit-text-stroke: 0px #0a59bd}
    span.sl {background-color: rgb(56,132,93);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 30px}
    span.smh {background-color: rgb(250,124,13);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 14px;}
    span.sn {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 8px;}
    span.sm {background-color: rgb(247,247,0);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
    span.sh {background-color: rgb(177,56,22);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 29px;}
    span.ta {background-color: rgb(56,132,93);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
    span.tawi {background-color: rgb(247,247,0);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
    span.nr {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 16px}
    span.bl {background-color: rgb(177,56,22);text-align: center;display: inline-block;font-size: 10.0px;color: white;font-weight: bold;border-radius: 3.0px;padding: 1px 20px}
    span.na {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 37px}
    span.sna {background-color: rgb(169,169,169);text-align: center;display: inline-block;font-size: 10.0px;color: black;font-weight: bold;border-radius: 3.0px;padding: 1px 34px}

    table.t1 {border-collapse: collapse; table-layout: fixed}
    td.td1 {vertical-align: middle; width: 125.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td2 {vertical-align: middle; width: 125.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td3 {vertical-align: middle; width: 858.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td4 {vertical-align: middle; width: 125.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td5 {vertical-align: middle; width: 125.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td6 {vertical-align: middle; width: 858.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td7 {vertical-align: middle; width: 136.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td8 {vertical-align: middle; width: 572.0px; min-width: 8.0px; background-color: #f1f2f5; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td10 {vertical-align: middle; width: 572.0px; min-width: 8.0px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #b4bbc5 #b4bbc5 #b4bbc5 #b4bbc5; padding: 7.0px 10.0px 7.0px 10.0px}
    td.td11 {vertical-align: middle; width: 50px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
    td.td12 {vertical-align: middle; width: 125px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
    td.td13 {vertical-align: middle; width: 125px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}
    td.td14 {vertical-align: middle; width: 858px; border-style: solid; border-width: 1.0px 1.0px 1.0px 1.0px; border-color: #ffffff #ffffff #ffffff #ffffff; padding: 1.0px 1.0px 1.0px 1.0px}



  </style>

</head>
<body>

<p class="p1"><span class="s2"><b>Executive Summary</b></span></p>
  <ul class="ul1">
    <li class="level-0"><span class="s1">Feature readiness status</span></li>
  </ul>
  <table cellspacing="0" cellpadding="0" class="t1">
    <tbody>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <p class="p5"><span class="s1"><b>SiVal</b></span></p>
        </td>
        <td valign="middle" class="td13">
          <p class="p5"><span class="s1"><b>Dev</b></span></p>
        </td>
        <td valign="middle" class="td14">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="USB2/3 Host Mode sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="USB2/3 Host Mode dev">
          </div>
        </td>
        <td valign="middle" class="td14">
          <div id="USB2/3 Host Mode radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="USB2/3 Device Mode sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="USB2/3 Device Mode dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="USB2/3 Device Mode radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="MultiFunction sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="MultiFunction dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="MultiFunction radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="CIO40 Link - PCIe Tunnel sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="CIO40 Link - PCIe Tunnel dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="CIO40 Link - PCIe Tunnel radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="CIO40 Link - USB Tunnel sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="CIO40 Link - USB Tunnel dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="CIO40 Link - USB Tunnel radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="CIO40 Link - DP Tunnel sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="CIO40 Link - DP Tunnel dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="CIO40 Link - DP Tunnel radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="Sleep/Wake sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="Sleep/Wake dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="Sleep/Wake radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="Reboot / ColdBoot sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="Reboot / ColdBoot dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="Reboot / ColdBoot radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="Performance sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="Performance dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="Performance radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="Compliance sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="Compliance dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="Compliance radar"></div></span></p>
        </td>
      </tr>
      <tr>
        <td valign="middle" class="td11">
          <p class="p4"><span class="s1"><br>
  </span></p>
        </td>
        <td valign="middle" class="td12">
          <div id="Stress sival">
          </div>
        </td>
        <td valign="middle" class="td13">
          <div id="Stress dev">
          </div>
        </td>
        <td valign="middle" class="td14"><div id="Stress radar"></div></span></p>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div id="Summary">
</div>

<p class="p7"><span class="s1"><b>Radar List</b></span></p>
<table cellspacing="0" cellpadding="0" class="t1">
  <tbody>
    <tr>
      <td valign="top" class="td1">
        <p class="p2"><span class="s2"><b>Area</b></span></p>
      </td>
      <td valign="top" class="td2">
        <p class="p2"><span class="s2"><b>Status</b></span></p>
      </td>
      <td valign="top" class="td3">
        <p class="p2"><span class="s2"><b>Summary</b></span></p>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td4">
        <p class="p6"><span class="s2"><b>USB IOT</b></span></p>
      </td>
      <td valign="top" class="td5">
        <div id="USB-IOT status">
        <p class="p3"><span class="smh"><b>MED-HIGH</b></span></p>
        </div>
      </td>
      <td valign="top" class="td6">
        <div id="USB-IOT"></div>
        </div>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td4">
        <p class="p6"><span class="s2"><b>CIO IOT</b></span></p>
      </td>
      <td valign="top" class="td5">
        <div id="CIO-IOT status">
        <p class="p3"><span class="smh"><b>MED-HIGH</b></span></p>
        </div>
      </td>
      <td valign="top" class="td6">
        <div id="CIO-IOT">
        <ul class="ul1">
          <li class="li1"><span class="s2">4/12 P0 IOT tests are completed</span></li>
        </ul>
        <p class="p1"><span class="smh"><b>MED-HIGH</b></span></p>
        <ul class="ul1">
          <li class="li1"><span class="s2">RT13 324 assert when hot-plug TBT FR devices[<a href="rdar://105844486"><span class="s4">rdar://105844486</span></a>]</span></li>
        </ul>
      </div>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td4">
        <p class="p6"><span class="s2"><b>SoC-Depth</b></span></p>
      </td>
      <td valign="top" class="td5">
          <div id="SoC-Depth status">
          <p class="p2"><span class="sn"><b>NOT STARTED</b></span></p>
          </div>
      </td>
      <td valign="top" class="td6">
      <div id="SoC-Depth">
        <p class="p1"><br></p>
      </div>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td4">
        <p class="p6"><span class="s2"><b>Stress Test</b></span></p>
      </td>
      <td valign="top" class="td5">
        <div id="Stress Test status">
        <p class="p3"><span class="sl"><b>LOW</b></span></p>
        </div>
      </td>
      <td valign="top" class="td6">
        <div id="Stress Test">
        <ul class="ul1">
          <li class="li1"><span class="s2">Test in progress , 60% completed</span></li>
        </ul>
        </div>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td4">
        <p class="p6"><span class="s2"><b>Compliance</b></span></p>
      </td>
      <td valign="top" class="td5">
        <div id="Compliance status">
        <p class="p3"><span class="sl"><b>LOW</b></span></p>
        </div>
      </td>
      <td valign="top" class="td6">
        <div id="Compliance">
        <ul class="ul1">
          <li class="li1"><span class="s2">USB3 host mode compliance completed</span></li>
        </ul>
      </div>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td4">
        <p class="p6"><span class="s2"><b>Performance</b></span></p>
      </td>
      <td valign="top" class="td5">
        <div id="Performance status">
        <p class="p3"><span class="sl"><b>LOW</b></span></p>
        </div>
      </td>
      <td valign="top" class="td6">
        <div id="Performance">
        <ul class="ul1">
          <li class="li1"><span class="s2">USB3 host mode compliance completed</span></li>
        </ul>
      </div>
      </td>
    </tr>
  </tbody>
</table>
<p class="p1"><br></p>
<p class="p1"><br></p>
<table cellspacing="0" cellpadding="0" class="t1">
  <tbody>
    <tr>
      <td valign="top" class="td7">
        <p class="p2"><span class="s2"><b>Quality Legend</b></span></p>
      </td>
      <td valign="top" class="td8">
        <p class="p4"><span class="s2"><b>Definition</b></span></p>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td5">
        <p class="p2"><span class="sl"><b>LOW</b></span></p>
      </td>
      <td valign="top" class="td10">
        <p class="p1"><span class="s2">No anticipated risks to meet objectives</span></p>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td5">
        <p class="p2"><span class="sm"><b>MEDIUM</b></span></p>
      </td>
      <td valign="top" class="td10">
        <p class="p1"><span class="s2">Minor none-silicon issues impacting customer quality</span><span class="s1"><br>
</span><span class="s2">Minor testing blocked from completion</span></p>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td5">
        <p class="p2"><span class="smh"><b>MED-HIGH</b></span></p>
      </td>
      <td valign="top" class="td10">
        <p class="p1"><span class="s2">Silicon issues currently in the milestone and on track to be fixed by TO date</span><span class="s1"><br>
</span><span class="s2">Critical none-silicon issues impacting customer quality</span></p>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td5">
        <p class="p2"><span class="sh"><b>HIGH</b></span></p>
      </td>
      <td valign="top" class="td10">
        <p class="p1"><span class="s2">Critical Silicon issues without path to closure by TO date</span><span class="s1"><br>
</span><span class="s2">Critical Silicon issues not screened or not in the milestone (deferred or otherwise)</span><span class="s1"><br>
</span><span class="s2">Significant testing blocked from completion</span></p>
      </td>
    </tr>
    <tr>
      <td valign="top" class="td5">
        <p class="p2"><span class="sn"><b>NOT STARTED</b></span></p>
      </td>
      <td valign="top" class="td10">
        <p class="p1"><span class="s2">Test have not started yet</span></p>
      </td>
    </tr>
  </tbody>
</table>
<p class="p1"><span class="s6"></span><br></p>
<p class="p1"><span class="s6"></span><br></p>
<p class="p1"><span class="s6"></span><br></p>
<p class="p1"><span class="s6"></span><br></p>
</body>
</html>