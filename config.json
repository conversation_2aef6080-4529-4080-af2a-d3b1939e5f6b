{"status_mappings": {"LOW": {"name": "LOW", "css_class": "sl", "color": "green", "description": "No anticipated risks to meet objectives"}, "MEDIUM": {"name": "MEDIUM", "css_class": "sm", "color": "yellow", "description": "Minor none-silicon issues impacting customer quality"}, "MED-HIGH": {"name": "MED-HIGH", "css_class": "smh", "color": "orange", "description": "Silicon issues currently in the milestone and on track to be fixed by TO date"}, "HIGH": {"name": "HIGH", "css_class": "sh", "color": "red", "description": "Critical Silicon issues without path to closure by TO date"}, "NOT STARTED": {"name": "NOT STARTED", "css_class": "sn", "color": "gray", "description": "Test have not started yet"}}, "feature_status_mappings": {"testable": {"css_class": "ta", "display_text": "TESTABLE"}, "issue": {"css_class": "tawi", "display_text": "TESTABLE"}, "notready": {"css_class": "nr", "display_text": "NOT READY"}, "na": {"css_class": "na", "display_text": "N/A"}, "blocked": {"css_class": "bl", "display_text": "BLOCKED"}}, "arrow_mappings": {"[up]": "&#8599;", "[down]": "&#8600;", "": "&#8594;"}, "html_templates": {"legend_table": "<p class=\"p1\"><br/></p><p class=\"p1\"><br/></p><table cellpadding=\"0\" cellspacing=\"0\" class=\"t1\"><tbody><tr><td class=\"td7\" valign=\"top\"><p class=\"p2\"><span class=\"s2\"><b>Quality Legend</b></span></p></td><td class=\"td8\" valign=\"top\"><p class=\"p4\"><span class=\"s2\"><b>Definition</b></span></p></td></tr>{{legend_rows}}</tbody></table><p class=\"p1\"><span class=\"s6\"></span><br/></p><p class=\"p1\"><span class=\"s6\"></span><br/></p><p class=\"p1\"><span class=\"s6\"></span><br/></p><p class=\"p1\"><span class=\"s6\"></span><br/></p>", "legend_row": "<tr><td class=\"td5\" valign=\"top\"><p class=\"p2\"><span class=\"{{css_class}}\"><b>{{status_name}}</b></span></p></td><td class=\"td10\" valign=\"top\"><p class=\"p1\"><span class=\"s2\">{{description}}</span></p></td></tr>"}, "header_replacements": {"<h1>Executive Summary</h1>": "", "<h1>Feature readiness status</h1>": "<p class=\"p1\"><span class=\"s2\"><b>Executive Summary</b></span></p>\n<ul class=\"ul1\">\n<li class=\"level-0\"><span class=\"s1\">Feature readiness status</span></li></ul>", "<h1>Radar List</h1>": "<p class=\"p7\"><span class=\"s1\"><b>Radar List</b></span></p>"}, "table_settings": {"first_table": {"class": "t1", "cellpadding": "0", "cellspacing": "0", "header_cell_classes": ["th11", "th12", "th13", "th14"], "data_cell_classes": ["td11", "td12", "td13", "td14"]}, "second_table": {"class": "t1", "cellpadding": "0", "cellspacing": "0"}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s"}}