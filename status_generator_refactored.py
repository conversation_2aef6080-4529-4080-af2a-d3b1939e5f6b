#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Status Generator - Refactored Version
将 Markdown 格式的状态报告转换为带样式的 HTML 报告的工具

重构后的版本具有更好的代码结构、错误处理和性能优化
"""

import re
import markdown
from bs4 import BeautifulSoup
import webbrowser
import argparse
import os
import json
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class StatusConfig:
    """状态配置数据类"""
    name: str
    css_class: str
    color: str
    description: str


class ConfigManager:
    """配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.status_mappings = {}
        self.css_classes = {}
        self.html_template = ""
        self._load_default_config()
        if config_file and Path(config_file).exists():
            self._load_config_file(config_file)
    
    def _load_default_config(self):
        """加载默认配置"""
        self.status_mappings = {
            'LOW': StatusConfig('LOW', 'sl', 'green', 'No anticipated risks to meet objectives'),
            'MEDIUM': StatusConfig('MEDIUM', 'sm', 'yellow', 'Minor none-silicon issues impacting customer quality'),
            'MED-HIGH': StatusConfig('MED-HIGH', 'smh', 'orange', 'Silicon issues currently in the milestone'),
            'HIGH': StatusConfig('HIGH', 'sh', 'red', 'Critical Silicon issues without path to closure'),
            'NOT STARTED': StatusConfig('NOT STARTED', 'sn', 'gray', 'Test have not started yet')
        }
        
        self.feature_status_mappings = {
            'testable': ('ta', 'TESTABLE'),
            'issue': ('tawi', 'TESTABLE'),
            'notready': ('nr', 'NOT READY'),
            'na': ('na', 'N/A'),
            'blocked': ('bl', 'BLOCKED')
        }
        
        self.arrow_mappings = {
            '[up]': '&#8599;',
            '[down]': '&#8600;',
            '': '&#8594;'
        }
    
    def _load_config_file(self, config_file: str):
        """从文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

                # 加载状态映射
                if 'status_mappings' in config_data:
                    for status, config in config_data['status_mappings'].items():
                        self.status_mappings[status] = StatusConfig(
                            name=config['name'],
                            css_class=config['css_class'],
                            color=config['color'],
                            description=config['description']
                        )

                # 加载功能状态映射
                if 'feature_status_mappings' in config_data:
                    for status, config in config_data['feature_status_mappings'].items():
                        self.feature_status_mappings[status] = (
                            config['css_class'],
                            config['display_text']
                        )

                # 加载箭头映射
                if 'arrow_mappings' in config_data:
                    self.arrow_mappings.update(config_data['arrow_mappings'])

                # 加载其他配置
                self.html_templates = config_data.get('html_templates', {})
                self.header_replacements = config_data.get('header_replacements', {})
                self.table_settings = config_data.get('table_settings', {})

                logger.info(f"配置文件加载成功: {config_file}")
        except Exception as e:
            logger.warning(f"配置文件加载失败: {e}，使用默认配置")
    
    def get_status_config(self, status: str) -> Optional[StatusConfig]:
        """获取状态配置"""
        return self.status_mappings.get(status.upper())
    
    def get_feature_status_mapping(self, status: str) -> Optional[Tuple[str, str]]:
        """获取功能状态映射"""
        return self.feature_status_mappings.get(status.lower())


class TextProcessor:
    """文本处理工具类"""
    
    # 预编译正则表达式以提高性能
    STATUS_PATTERN = re.compile(r'\[(LOW|MEDIUM|MED-HIGH|HIGH|NOT STARTED)\]', re.IGNORECASE)
    # 支持两种rdar格式：
    # 1. 新格式: rdar://146598443 (radar titles)
    # 2. 旧格式: radar title (rdar://146598443) 或 [rdar://146598443]
    RDAR_PATTERN_NEW = re.compile(r'(rdar://\d+)\s*\(([^)]+)\)')  # 新格式
    RDAR_PATTERN_OLD = re.compile(r'[\[\(](rdar://[^\]\)]+?)[\]\)]')  # 旧格式
    HEADER_PATTERN = re.compile(r'###\s+(.+)')
    BULLET_PATTERN = re.compile(r'^(\s*)-\s*(.*)')
    INDENT_PATTERN = re.compile(r'^(\t| {4})*')
    
    @staticmethod
    def get_indent_level(line: str) -> int:
        """
        优化的缩进级别检测
        使用正则表达式替代循环，提高性能
        """
        match = TextProcessor.INDENT_PATTERN.match(line)
        if match:
            indent_str = match.group(0)
            return indent_str.count('\t') + indent_str.count('    ')
        return 0
    
    @staticmethod
    def convert_rdar_to_links(text: str) -> str:
        """将 rdar 链接转换为 HTML 超链接，支持新旧两种格式"""

        # 处理新格式: rdar://146598443 (radar titles) -> radar titles (rdar://146598443)
        def replace_new_format(match):
            rdar_url = match.group(1)  # rdar://146598443
            title = match.group(2)     # radar titles
            return f'{title} (<a href="{rdar_url}"><span class="s4">{rdar_url}</span></a>)'

        # 处理旧格式: (rdar://146598443) 或 [rdar://146598443]
        def replace_old_format(match):
            rdar_url = match.group(1)  # rdar://146598443
            return f'<a href="{rdar_url}"><span class="s4">({rdar_url})</span></a>'

        # 先处理新格式，再处理旧格式
        text = TextProcessor.RDAR_PATTERN_NEW.sub(replace_new_format, text)
        text = TextProcessor.RDAR_PATTERN_OLD.sub(replace_old_format, text)

        return text
    
    @staticmethod
    def extract_status_from_text(text: str) -> Tuple[Optional[str], str]:
        """从文本中提取状态信息"""
        match = TextProcessor.STATUS_PATTERN.search(text.upper())
        if match:
            status = match.group(1)
            clean_text = text.replace(f'[{status}]', '').replace(f'[{status.lower()}]', '').strip()
            return status, clean_text
        return None, text


class MarkdownSectionProcessor:
    """Markdown 章节处理器基类"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
    
    def process(self, content: str) -> str:
        """处理章节内容，子类需要实现此方法"""
        raise NotImplementedError


class ExecutiveSummaryProcessor(MarkdownSectionProcessor):
    """执行摘要处理器"""
    
    def process(self, content: str) -> str:
        """处理执行摘要章节"""
        lines = content.splitlines()
        processed_lines = []
        current_level = -1
        
        for line in lines:
            if line.strip().startswith('-'):
                indent = TextProcessor.get_indent_level(line)
                text_content = line.strip()[2:].strip()
                
                # 处理嵌套列表的开闭标签
                while current_level >= 0 and indent <= current_level:
                    processed_lines.append('</ul>')
                    current_level -= 1
                
                if current_level < indent:
                    processed_lines.append('<ul>')
                    current_level = indent
                
                processed_lines.append(f'<li class="level-{indent}">{text_content}</li>')
            else:
                # 关闭所有打开的列表
                while current_level >= 0:
                    processed_lines.append('</ul>')
                    current_level -= 1
                processed_lines.append(line)
        
        # 关闭剩余的列表
        while current_level >= 0:
            processed_lines.append('</ul>')
            current_level -= 1
        
        return "\n".join(processed_lines)


class RadarListProcessor(MarkdownSectionProcessor):
    """雷达列表处理器"""
    
    def process(self, content: str) -> str:
        """处理雷达列表章节，生成表格"""
        lines = content.splitlines()
        table_lines = []
        area = None
        status = None
        summary_items = []
        current_indent_level = 0
        expecting_status = False
        
        for line in lines:
            # 检查区域标题
            header_match = TextProcessor.HEADER_PATTERN.match(line)
            if header_match:
                if area and summary_items:
                    summary_html = "".join(summary_items) + "</ul>" * current_indent_level
                    table_lines.append(f"| {area} | {status} | {summary_html} |")
                    summary_items = []
                    current_indent_level = 0
                
                area = header_match.group(1)
                status = None
                expecting_status = True
                continue
            
            # 处理列表项
            bullet_match = TextProcessor.BULLET_PATTERN.match(line)
            if bullet_match:
                indent = TextProcessor.get_indent_level(line)
                text = bullet_match.group(2).strip()
                
                if expecting_status:
                    status = self._process_status_line(text)
                    expecting_status = False
                else:
                    self._process_summary_line(text, indent, summary_items, current_indent_level)
                    current_indent_level = indent
        
        # 处理最后一个区域
        if area and summary_items:
            summary_html = "".join(summary_items) + "</ul>" * current_indent_level
            table_lines.append(f"| {area} | {status} | {summary_html} |")
        
        header = "| Area | Status | Summary |\n| --- | --- | --- |"
        return header + "\n" + "\n".join(table_lines)
    
    def _process_status_line(self, text: str) -> str:
        """处理状态行"""
        # 移除方向指示符
        clean_text = text.replace('[up]', '').replace('[down]', '').strip()
        status_name = clean_text.upper()
        
        status_config = self.config.get_status_config(status_name)
        if not status_config:
            return text
        
        # 添加方向箭头
        indicator = "[down]" if "[down]" in text.lower() else "[up]" if "[up]" in text.lower() else ""
        arrow = self.config.arrow_mappings.get(indicator, '&#8594;')
        
        return f'<p class="p1">{arrow}<span class="{status_config.css_class}"><b>{status_config.name}</b></span></p>'
    
    def _process_summary_line(self, text: str, indent: int, summary_items: List[str], current_indent_level: int):
        """处理摘要行"""
        if indent > current_indent_level:
            summary_items.append("<ul>" * (indent - current_indent_level))
        elif indent < current_indent_level:
            summary_items.append("</ul>" * (current_indent_level - indent))
        
        # 检查是否包含状态标签
        status, clean_text = TextProcessor.extract_status_from_text(text)
        
        if status:
            status_config = self.config.get_status_config(status)
            if status_config:
                summary_items.append(f'<p class="p1"><span class="{status_config.css_class}"><b>{status}</b></span></p>{clean_text}')
            else:
                summary_items.append(f'<li class="level-{indent}">{text}</li>')
        else:
            summary_items.append(f'<li class="level-{indent}">{text}</li>')


class FeatureStatusProcessor(MarkdownSectionProcessor):
    """功能状态处理器"""
    
    def process(self, content: str) -> str:
        """处理功能状态章节，保持原有格式"""
        return content


class MarkdownConverter:
    """Markdown 转换器主类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = ConfigManager(config_file)
        self.processors = {
            "Executive Summary": ExecutiveSummaryProcessor(self.config),
            "Radar List": RadarListProcessor(self.config),
            "Feature readiness status": FeatureStatusProcessor(self.config)
        }
    
    def convert_markdown(self, md_text: str) -> str:
        """转换 Markdown 文本"""
        sections = self._split_sections(md_text)
        output = []
        
        for title, content in sections.items():
            processor = self.processors.get(title)
            if processor:
                processed_content = processor.process(content)
                output.append(f"# {title}\n\n{processed_content}")
            else:
                # 默认处理
                output.append(f"# {title}\n{content}")
        
        return "\n\n".join(output)
    
    def _split_sections(self, md_text: str) -> Dict[str, str]:
        """将 Markdown 文本分割为章节"""
        sections = re.split(r'(?m)^# ', md_text)
        section_dict = {}
        
        for section in sections:
            if not section.strip():
                continue
            lines = section.splitlines()
            title = lines[0].strip()
            content = "\n".join(lines[1:]).strip()
            section_dict[title] = content
        
        return section_dict


class HTMLProcessor:
    """HTML 处理器类"""

    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager

    def process_html(self, html_content: str) -> str:
        """处理 HTML 内容，添加样式和格式"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # 处理表格
        self._process_tables(soup)

        # 转换 rdar 链接
        final_html = str(soup)
        final_html = TextProcessor.convert_rdar_to_links(final_html)

        # 替换标题
        final_html = self._replace_headers(final_html)

        return final_html

    def _process_tables(self, soup: BeautifulSoup):
        """处理表格样式"""
        tables = soup.find_all('table')

        if len(tables) >= 1:
            self._process_first_table(tables[0], soup)

        if len(tables) >= 2:
            self._process_second_table(tables[1], soup)

    def _process_first_table(self, table, soup: BeautifulSoup):
        """处理第一个表格（功能状态表格）"""
        table['cellpadding'] = "0"
        table['cellspacing'] = "0"
        table['class'] = table.get('class', []) + ['t1']

        rows = table.find_all('tr')
        if not rows:
            return

        # 处理表头
        header_row = rows[0]
        for index, cell in enumerate(header_row.find_all(['th', 'td'])):
            if index <= 1:
                self._format_header_cell(cell, soup, 's1', 'p5')
            elif index <= 3:
                self._format_header_cell(cell, soup, 's1', 'p4')

        # 处理数据行
        for row in rows[1:]:
            for index, cell in enumerate(row.find_all(['th', 'td'], recursive=False)):
                if index <= 1:
                    self._format_status_cell(cell, soup)
                elif index <= 3:
                    self._format_text_cell(cell, soup, 's1', 'p9')

        # 设置单元格类
        self._set_table_cell_classes(rows, 'th1', 'td1')

    def _process_second_table(self, table, soup: BeautifulSoup):
        """处理第二个表格（雷达列表表格）"""
        table['cellpadding'] = "0"
        table['cellspacing'] = "0"
        table['class'] = table.get('class', []) + ['t1']

        # 处理表头
        header_row = table.find('tr')
        if header_row:
            for index, header_cell in enumerate(header_row.find_all(['th', 'td'], recursive=False)):
                header_cell.name = 'td'
                header_cell['class'] = [f'td{index + 1}']
                header_cell['valign'] = 'top'
                self._format_header_cell(header_cell, soup, 's2', 'p2')

        # 处理数据行
        data_rows = table.find_all('tr')[1:]
        for row in data_rows:
            for index, cell in enumerate(row.find_all(['td'])):
                cell['class'] = [f'td{index + 4}']
                cell['valign'] = 'top'
                if index == 0:
                    self._format_text_cell(cell, soup, 's2', 'p6', bold=True)

    def _format_header_cell(self, cell, soup: BeautifulSoup, span_class: str, p_class: str):
        """格式化表头单元格"""
        header_text = cell.get_text()
        cell.clear()

        p_tag = soup.new_tag("p", **{"class": p_class})
        span_tag = soup.new_tag("span", **{"class": span_class})
        bold_tag = soup.new_tag("b")
        bold_tag.string = header_text
        span_tag.append(bold_tag)
        p_tag.append(span_tag)
        cell.append(p_tag)

    def _format_status_cell(self, cell, soup: BeautifulSoup):
        """格式化状态单元格"""
        text = cell.get_text().strip().lower()
        mapping = self.config.get_feature_status_mapping(text)

        if mapping:
            css_class, display_text = mapping
            p = soup.new_tag('p', **{'class': 'p8'})
            span = soup.new_tag('span', **{'class': css_class})
            bold = soup.new_tag('b')
            bold.string = display_text
            span.append(bold)
            cell.clear()
            p.append(span)
            cell.append(p)

    def _format_text_cell(self, cell, soup: BeautifulSoup, span_class: str, p_class: str, bold: bool = False):
        """格式化文本单元格"""
        text = cell.get_text()
        cell.clear()

        p_tag = soup.new_tag("p", **{"class": p_class})
        span_tag = soup.new_tag("span", **{"class": span_class})

        if bold:
            bold_tag = soup.new_tag("b")
            bold_tag.string = text
            span_tag.append(bold_tag)
        else:
            span_tag.string = text

        p_tag.append(span_tag)
        cell.append(p_tag)

    def _set_table_cell_classes(self, rows, th_prefix: str, td_prefix: str):
        """设置表格单元格类"""
        for row in rows:
            header_cells = row.find_all('th')
            for i, cell in enumerate(header_cells):
                cell['class'] = [f'{th_prefix}{i+1}']

            data_cells = row.find_all('td')
            for i, cell in enumerate(data_cells):
                cell['class'] = [f'{td_prefix}{i+1}']

    def _replace_headers(self, html: str) -> str:
        """替换标题为样式化版本"""
        replacements = {
            '<h1>Executive Summary</h1>': '',
            '<h1>Feature readiness status</h1>': '''<p class="p1"><span class="s2"><b>Executive Summary</b></span></p>
<ul class="ul1">
<li class="level-0"><span class="s1">Feature readiness status</span></li></ul>''',
            '<h1>Radar List</h1>': '<p class="p7"><span class="s1"><b>Radar List</b></span></p>'
        }

        for old, new in replacements.items():
            html = html.replace(old, new)

        return html


class FileHandler:
    """文件处理工具类"""

    @staticmethod
    def safe_read_file(file_path: str) -> str:
        """安全读取文件"""
        try:
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError as e:
            raise ValueError(f"文件编码错误 {file_path}: {e}")
        except Exception as e:
            raise IOError(f"读取文件失败 {file_path}: {e}")

    @staticmethod
    def safe_write_file(file_path: str, content: str):
        """安全写入文件"""
        try:
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"文件写入成功: {file_path}")
        except Exception as e:
            raise IOError(f"写入文件失败 {file_path}: {e}")


class StatusReportGenerator:
    """状态报告生成器主类"""

    def __init__(self, config_file: Optional[str] = None):
        self.config = ConfigManager(config_file)
        self.markdown_converter = MarkdownConverter(config_file)
        self.html_processor = HTMLProcessor(self.config)

    def generate_report(self, md_file_path: str, html_file_path: str, css_file_path: Optional[str] = None):
        """生成状态报告"""
        try:
            logger.info(f"开始生成报告: {md_file_path} -> {html_file_path}")

            # 读取 Markdown 文件
            md_content = FileHandler.safe_read_file(md_file_path)

            # 转换 Markdown (包含状态标签处理)
            converted_md = self.markdown_converter.convert_markdown(md_content)

            # 转换为 HTML
            html_content = markdown.markdown(converted_md, extensions=['tables'])

            # 处理 HTML
            processed_html = self.html_processor.process_html(html_content)

            # 生成完整 HTML
            full_html = self._generate_full_html(processed_html, css_file_path)

            # 写入文件
            FileHandler.safe_write_file(html_file_path, full_html)

            # 打开浏览器
            self._open_in_browser(html_file_path)

            logger.info("报告生成完成")

            return full_html

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            raise



    def _generate_full_html(self, body_content: str, css_file_path: Optional[str] = None) -> str:
        """生成完整的 HTML 文档"""
        css_content = ""
        if css_file_path:
            try:
                css_content = FileHandler.safe_read_file(css_file_path)
                css_content = f"<style>\n{css_content}\n</style>"
            except Exception as e:
                logger.warning(f"CSS 文件读取失败: {e}")

        legend_table = self._generate_legend_table()

        return f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Status Report</title>
    {css_content}
</head>
<body>
{body_content}
{legend_table}
</body>
</html>"""

    def _generate_legend_table(self) -> str:
        """生成图例表格"""
        return """
<p class="p1"><br/></p>
<p class="p1"><br/></p>
<table cellpadding="0" cellspacing="0" class="t1">
<tbody>
<tr>
<td class="td7" valign="top">
<p class="p2"><span class="s2"><b>Quality Legend</b></span></p>
</td>
<td class="td8" valign="top">
<p class="p4"><span class="s2"><b>Definition</b></span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sl"><b>LOW</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">No anticipated risks to meet objectives</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sm"><b>MEDIUM</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Minor none-silicon issues impacting customer quality</span><span class="s1"><br/>
</span><span class="s2">Minor testing blocked from completion</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="smh"><b>MED-HIGH</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Silicon issues currently in the milestone and on track to be fixed by TO date</span><span class="s1"><br/>
</span><span class="s2">Critical none-silicon issues impacting customer quality</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sh"><b>HIGH</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Critical Silicon issues without path to closure by TO date</span><span class="s1"><br/>
</span><span class="s2">Critical Silicon issues not screened or not in the milestone (deferred or otherwise)</span><span class="s1"><br/>
</span><span class="s2">Significant testing blocked from completion</span></p>
</td>
</tr>
<tr>
<td class="td5" valign="top">
<p class="p2"><span class="sn"><b>NOT STARTED</b></span></p>
</td>
<td class="td10" valign="top">
<p class="p1"><span class="s2">Test have not started yet</span></p>
</td>
</tr>
</tbody>
</table>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>
<p class="p1"><span class="s6"></span><br/></p>"""

    def _open_in_browser(self, html_file_path: str):
        """在浏览器中打开文件"""
        try:
            file_url = f'file://{os.path.abspath(html_file_path)}'
            webbrowser.open(file_url)
            logger.info(f"已在浏览器中打开: {file_url}")
        except Exception as e:
            logger.warning(f"无法打开浏览器: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Convert a markdown report to HTML with tags.")
    parser.add_argument('--md', type=str, default=None,
                       help='Path to the markdown file (default: report.md in script directory)')
    parser.add_argument('--html', type=str, default=None,
                       help='Path to the output HTML file (default: report.html in script directory)')
    parser.add_argument('--css', type=str, default=None,
                       help='Path to the CSS file (default: style.css in script directory)')
    parser.add_argument('--config', type=str, default=None,
                       help='Path to the configuration file')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 设置默认文件路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    md_file = args.md if args.md else os.path.join(script_dir, 'report.md')
    html_file = args.html if args.html else os.path.join(script_dir, 'report.html')
    css_file = args.css if args.css else os.path.join(script_dir, 'style.css')

    try:
        generator = StatusReportGenerator(args.config)
        generator.generate_report(md_file, html_file, css_file)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
