# fix_markdown 功能指南

## 📋 概述

`fix_markdown` 是MCP Server的新功能，可以根据`format_check`的结果自动修复Markdown文件的常见格式问题。

## 🎯 功能特点

### 自动修复的问题

1. **添加缺失的必需部分**
   - Feature readiness status
   - Executive Summary
   - Radar List

2. **修复表格格式**
   - 将纯文本转换为Markdown表格
   - 添加表格分隔符

3. **添加区域标题**
   - 为Radar List添加 `###` 区域标题
   - 默认使用 `### General`

4. **添加状态标签**
   - 为缺少状态标签的区域添加默认的 `LOW` 标签
   - 保持现有的状态标签不变

## 🚀 使用方法

### 方法1: 通过MCP Server

```python
from server import fix_markdown, FixMarkdownRequest
from fastmcp import Context

# 读取需要修复的Markdown内容
with open('broken.md', 'r') as f:
    markdown_content = f.read()

# 调用fix_markdown
ctx = Context()
request = FixMarkdownRequest(markdown_content=markdown_content)
result = await fix_markdown(ctx, request)

# 查看结果
print(f"修复后的内容:\n{result.fixed_content}")
print(f"\n修改列表:")
for change in result.changes_made:
    print(f"  - {change}")
print(f"\n格式是否正确: {result.is_now_valid}")
if result.remaining_errors:
    print(f"剩余错误: {result.remaining_errors}")
```

### 方法2: 使用测试脚本

```bash
cd status-generator-mcp
python3 test_fix_markdown.py
```

### 方法3: 批量修复测试文件

```python
from test_fix_markdown import fix_markdown_logic

# 读取文件
with open('test_md_files/02_missing_feature_status.md', 'r') as f:
    content = f.read()

# 修复
result = fix_markdown_logic(content)

# 保存修复后的文件
with open('fixed.md', 'w') as f:
    f.write(result['fixed_content'])
```

## 📊 修复示例

### 示例1: 缺少必需部分

**输入**:
```markdown
# Executive Summary
- Summary item

# Radar List
### USB-IOT
- LOW
    - Completed
```

**输出**:
```markdown
# Executive Summary
- Summary item

# Radar List
### USB-IOT
- LOW
    - Completed

# Feature readiness status
| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|Feature 1|
|testable|testable|Feature 2|
```

**修改**:
- ✓ Added missing section: '# Feature readiness status' with template table

---

### 示例2: 表格格式错误

**输入**:
```markdown
# Feature readiness status
This is not a table, just plain text.

# Executive Summary
- Summary

# Radar List
### USB-IOT
- LOW
    - Done
```

**输出**:
```markdown
# Feature readiness status
| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|This is not a table, just plain text.|

# Executive Summary
- Summary

# Radar List
### USB-IOT
- LOW
    - Done
```

**修改**:
- ✓ Converted 'Feature readiness status' plain text to table format

---

### 示例3: 缺少区域标题

**输入**:
```markdown
# Feature readiness status
| Sival | Dev | Feature |
|---|---|---|
|testable|testable|Feature 1|

# Executive Summary
- Summary

# Radar List
- LOW
    - Items without area header
```

**输出**:
```markdown
# Feature readiness status
| Sival | Dev | Feature |
|---|---|---|
|testable|testable|Feature 1|

# Executive Summary
- Summary

# Radar List
### General
- LOW
    - Items without area header
```

**修改**:
- ✓ Added default area header '### General' to Radar List

---

### 示例4: 缺少状态标签

**输入**:
```markdown
# Feature readiness status
| Sival | Dev | Feature |
|---|---|---|
|testable|testable|Feature 1|

# Executive Summary
- Summary

# Radar List
### USB-IOT
- Item without status
### Another Area
- Also no status
```

**输出**:
```markdown
# Feature readiness status
| Sival | Dev | Feature |
|---|---|---|
|testable|testable|Feature 1|

# Executive Summary
- Summary

# Radar List
### USB-IOT
- LOW
    - Items completed
- Item without status
### Another Area
- LOW
    - Items completed
- Also no status
```

**修改**:
- ✓ Added default status tag 'LOW' to area 'USB-IOT'
- ✓ Added default status tag 'LOW' to area 'Another Area'

---

### 示例5: 缺少所有必需部分

**输入**:
```markdown
Some random text
```

**输出**:
```markdown
# Feature readiness status
| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|Feature 1|
|testable|testable|Feature 2|

# Executive Summary
- Summary item 1
    - Detail 1

# Radar List
### Area 1
- LOW
    - Completed items
```

**修改**:
- ✓ Added missing section: '# Feature readiness status' with template table
- ✓ Added missing section: '# Executive Summary' with template content
- ✓ Added missing section: '# Radar List' with template content

## 🧪 测试结果

运行 `test_fix_markdown.py` 的结果：

```
================================================================================
fix_markdown 功能测试
================================================================================

测试 1: 缺少Feature readiness status
   ✅ 测试通过 - 修复后格式正确

测试 2: 缺少所有必需部分
   ✅ 测试通过 - 修复后格式正确

测试 3: 表格格式错误
   ✅ 测试通过 - 修复后格式正确

测试 4: 缺少区域标题
   ✅ 测试通过 - 修复后格式正确

测试 5: 缺少状态标签
   ✅ 测试通过 - 修复后格式正确

================================================================================
测试总结
================================================================================
总测试数: 5
✅ 通过: 5
❌ 失败: 0
成功率: 100.0%

🎉 所有测试通过！fix_markdown功能正常
```

## 📝 返回值说明

`fix_markdown` 返回一个包含以下字段的对象：

| 字段 | 类型 | 说明 |
|------|------|------|
| `fixed_content` | string | 修复后的Markdown内容 |
| `changes_made` | List[string] | 所做修改的列表 |
| `is_now_valid` | boolean | 修复后是否通过format_check |
| `remaining_errors` | List[string] | 剩余的错误（如果有） |

## ⚠️ 注意事项

### 自动修复的限制

1. **模板内容**: 添加的内容是模板，需要用户手动填写实际信息
2. **保留原有内容**: 修复不会删除或修改现有的正确内容
3. **默认值**: 使用默认值（如 `LOW` 状态，`testable` 状态）
4. **不修复的问题**: 
   - 拼写错误
   - 内容逻辑错误
   - 复杂的格式问题

### 最佳实践

1. **先检查后修复**: 使用 `format_check` 查看具体错误
2. **审查修复结果**: 检查 `changes_made` 了解做了哪些修改
3. **手动完善**: 修复后需要填写实际的内容
4. **再次验证**: 使用 `format_check` 确认修复后的格式

## 🔄 工作流程

推荐的使用流程：

```
1. 编写Markdown文件
   ↓
2. 运行 format_check
   ↓
3. 如果有错误 → 运行 fix_markdown
   ↓
4. 审查修复结果
   ↓
5. 手动完善模板内容
   ↓
6. 再次运行 format_check
   ↓
7. 运行 generate_report
```

## 💡 使用技巧

### 技巧1: 批量修复

```bash
# 修复所有错误格式的测试文件
for file in test_md_files/*_missing_*.md; do
    python3 -c "
from test_fix_markdown import fix_markdown_logic
with open('$file', 'r') as f:
    result = fix_markdown_logic(f.read())
with open('${file%.md}_fixed.md', 'w') as f:
    f.write(result['fixed_content'])
print('Fixed: $file')
"
done
```

### 技巧2: 查看修改详情

```python
result = fix_markdown_logic(content)
print(f"修改数量: {len(result['changes_made'])}")
for i, change in enumerate(result['changes_made'], 1):
    print(f"{i}. {change}")
```

### 技巧3: 条件修复

```python
# 只在特定错误时修复
check_result = test_format_check_logic(content)
if "Missing required section" in str(check_result['errors']):
    result = fix_markdown_logic(content)
    # 使用修复后的内容
```

## 📖 相关文档

- `README.md` - MCP Server完整文档
- `FORMAT_CHECK_TEST_REPORT.md` - format_check测试报告
- `test_fix_markdown.py` - fix_markdown测试脚本
- `server.py` - fix_markdown实现代码

---

**fix_markdown功能让Markdown格式修复变得简单快捷！** ✨

