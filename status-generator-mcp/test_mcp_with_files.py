#!/usr/bin/env python3
"""
使用test_md_files中的文件测试MCP Server
测试format_check和generate_report功能
"""

import os
import sys
import subprocess
from pathlib import Path

SCRIPT_DIR = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_DIR / "test_md_files"
OUTPUT_DIR = SCRIPT_DIR / "test_outputs"

# 测试用例配置
TEST_CASES = {
    "01_valid_complete.md": {
        "name": "✅ 完整正确的格式",
        "should_pass_format_check": True,
        "should_generate_html": True
    },
    "02_missing_feature_status.md": {
        "name": "❌ 缺少Feature readiness status",
        "should_pass_format_check": False,
        "should_generate_html": False
    },
    "03_missing_executive_summary.md": {
        "name": "❌ 缺少Executive Summary",
        "should_pass_format_check": False,
        "should_generate_html": False
    },
    "04_missing_radar_list.md": {
        "name": "❌ 缺少Radar List",
        "should_pass_format_check": False,
        "should_generate_html": False
    },
    "05_invalid_table_format.md": {
        "name": "❌ 表格格式错误",
        "should_pass_format_check": False,
        "should_generate_html": False
    },
    "06_missing_area_headers.md": {
        "name": "❌ 缺少区域标题",
        "should_pass_format_check": False,
        "should_generate_html": False
    },
    "07_missing_status_tags.md": {
        "name": "❌ 缺少状态标签",
        "should_pass_format_check": False,
        "should_generate_html": False
    },
    "08_valid_with_rdar_links.md": {
        "name": "✅ 包含rdar链接",
        "should_pass_format_check": True,
        "should_generate_html": True
    },
    "09_valid_with_nested_lists.md": {
        "name": "✅ 深层嵌套列表",
        "should_pass_format_check": True,
        "should_generate_html": True
    },
    "10_empty_executive_summary.md": {
        "name": "✅ 空的Executive Summary",
        "should_pass_format_check": True,
        "should_generate_html": True
    }
}

def read_file(filepath):
    """读取文件内容"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def test_format_check(markdown_content):
    """测试format_check功能"""
    sys.path.insert(0, str(SCRIPT_DIR))
    from test_format_check import test_format_check_logic
    return test_format_check_logic(markdown_content)

def test_generate_report(md_file_path, output_html_path):
    """测试generate_report功能（模拟MCP调用）"""
    status_gen = SCRIPT_DIR / "status_generator.py"
    config = SCRIPT_DIR / "config.json"
    css = SCRIPT_DIR / "style.css"
    
    cmd = [
        "python3",
        str(status_gen),
        "--md", str(md_file_path),
        "--html", str(output_html_path)
    ]
    
    if config.exists():
        cmd.extend(["--config", str(config)])
    if css.exists():
        cmd.extend(["--css", str(css)])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True, timeout=10)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error: {e.stderr}"
    except subprocess.TimeoutExpired:
        return False, "Timeout"

def run_tests():
    """运行所有测试"""
    print("=" * 80)
    print("MCP Server 文件测试")
    print("=" * 80)
    print(f"测试文件目录: {TEST_FILES_DIR}")
    print(f"输出目录: {OUTPUT_DIR}")
    print("=" * 80)
    
    # 创建输出目录
    OUTPUT_DIR.mkdir(exist_ok=True)
    
    # 统计
    total_tests = len(TEST_CASES)
    format_check_passed = 0
    format_check_failed = 0
    generate_passed = 0
    generate_failed = 0
    
    # 运行每个测试
    for filename, config in TEST_CASES.items():
        print(f"\n{'=' * 80}")
        print(f"测试: {config['name']}")
        print(f"文件: {filename}")
        print(f"{'=' * 80}")
        
        md_file = TEST_FILES_DIR / filename
        if not md_file.exists():
            print(f"❌ 文件不存在: {md_file}")
            continue
        
        # 读取内容
        markdown_content = read_file(md_file)
        print(f"\n📄 文件大小: {len(markdown_content)} 字符")
        
        # 测试1: format_check
        print(f"\n🔍 测试 format_check:")
        format_result = test_format_check(markdown_content)
        
        is_valid = format_result['is_valid']
        expected_valid = config['should_pass_format_check']
        
        if is_valid == expected_valid:
            print(f"   ✅ format_check 结果正确: is_valid={is_valid}")
            format_check_passed += 1
        else:
            print(f"   ❌ format_check 结果错误: 期望={expected_valid}, 实际={is_valid}")
            format_check_failed += 1
        
        if not is_valid:
            print(f"\n   错误 ({len(format_result['errors'])}):")
            for i, error in enumerate(format_result['errors'][:3], 1):  # 只显示前3个
                print(f"      {i}. {error}")
            if len(format_result['errors']) > 3:
                print(f"      ... 还有 {len(format_result['errors']) - 3} 个错误")
        
        # 测试2: generate_report（只对应该通过的文件）
        if config['should_generate_html']:
            print(f"\n🔧 测试 generate_report:")
            output_html = OUTPUT_DIR / filename.replace('.md', '.html')
            
            success, message = test_generate_report(md_file, output_html)
            
            if success:
                print(f"   ✅ 成功生成HTML: {output_html.name}")
                if output_html.exists():
                    html_size = output_html.stat().st_size
                    print(f"   📊 HTML大小: {html_size:,} 字节")
                    generate_passed += 1
                else:
                    print(f"   ❌ HTML文件未生成")
                    generate_failed += 1
            else:
                print(f"   ❌ 生成失败: {message}")
                generate_failed += 1
        else:
            print(f"\n⏭️  跳过 generate_report（格式不正确）")
    
    # 总结
    print(f"\n{'=' * 80}")
    print(f"测试总结")
    print(f"{'=' * 80}")
    print(f"\n📊 format_check 测试:")
    print(f"   总数: {total_tests}")
    print(f"   ✅ 通过: {format_check_passed}")
    print(f"   ❌ 失败: {format_check_failed}")
    print(f"   成功率: {format_check_passed / total_tests * 100:.1f}%")
    
    valid_files = sum(1 for c in TEST_CASES.values() if c['should_generate_html'])
    print(f"\n📊 generate_report 测试:")
    print(f"   总数: {valid_files}")
    print(f"   ✅ 通过: {generate_passed}")
    print(f"   ❌ 失败: {generate_failed}")
    if valid_files > 0:
        print(f"   成功率: {generate_passed / valid_files * 100:.1f}%")
    
    # 生成的HTML文件列表
    html_files = list(OUTPUT_DIR.glob("*.html"))
    if html_files:
        print(f"\n📁 生成的HTML文件 ({len(html_files)}):")
        for html_file in sorted(html_files):
            size = html_file.stat().st_size
            print(f"   • {html_file.name} ({size:,} 字节)")
    
    # 最终结果
    print(f"\n{'=' * 80}")
    if format_check_failed == 0 and generate_failed == 0:
        print("🎉 所有测试通过！MCP Server功能正常")
        return 0
    else:
        print(f"⚠️  有 {format_check_failed + generate_failed} 个测试失败")
        return 1

def main():
    """主函数"""
    # 检查测试文件目录
    if not TEST_FILES_DIR.exists():
        print(f"❌ 测试文件目录不存在: {TEST_FILES_DIR}")
        return 1
    
    # 检查测试文件
    missing_files = []
    for filename in TEST_CASES.keys():
        if not (TEST_FILES_DIR / filename).exists():
            missing_files.append(filename)
    
    if missing_files:
        print(f"❌ 缺少测试文件:")
        for f in missing_files:
            print(f"   • {f}")
        return 1
    
    # 运行测试
    return run_tests()

if __name__ == "__main__":
    sys.exit(main())

