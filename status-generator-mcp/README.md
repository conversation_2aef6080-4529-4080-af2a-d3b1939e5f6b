# Status Generator MCP Server

一个基于FastMCP的状态报告生成服务器，用于将Markdown格式的状态报告转换为专业样式的HTML报告。

## 🚀 功能特点

- **完整的Markdown支持**: 支持三个主要部分（Feature readiness status、Executive Summary、Radar List）
- **智能格式转换**: 自动处理表格、列表、状态标签和rdar链接
- **自动格式修复**: 根据format_check结果自动修复常见格式问题 ✨ **新功能**
- **配置化设计**: 支持自定义CSS样式和配置文件
- **格式验证**: 提供格式检查工具，确保输入符合要求
- **使用指南**: 内置详细的格式说明和示例

## 📁 项目结构

```
status-generator-mcp/
├── server.py                    # MCP服务器主文件
├── status_generator.py          # 核心转换引擎
├── config.json                  # 配置文件
├── style.css                    # CSS样式文件
├── test_simple.py              # 测试脚本
├── TEST_REPORT.md              # 测试报告
├── generated_report.html       # 示例输出
└── README.md                   # 本文件
```

## 🛠 安装

### 前置要求

- Python 3.7+
- FastMCP库

### 安装步骤

```bash
# 安装FastMCP
pip install fastmcp

# 克隆或下载项目
cd status-generator-mcp
```

## 📖 使用方法

### 1. 启动MCP服务器

```bash
python3 server.py
```

### 2. 可用的MCP工具

#### `report_guide`
获取Markdown格式指南，包含每个部分的说明和示例。

```python
# 返回格式指南
guide = await report_guide(ctx)
```

#### `format_check`
检查Markdown内容是否符合格式要求。

```python
request = FormatCheckRequest(markdown_content="...")
result = await format_check(ctx, request)
# 返回: is_valid, errors, suggestions
```

#### `generate_report`
生成HTML状态报告。

```python
request = GenerateReportRequest(markdown_content="...")
result = await generate_report(ctx, request)
# 返回: html_report, message
```

#### `fix_markdown` ✨ **新功能**
自动修复Markdown格式问题。

```python
request = FixMarkdownRequest(markdown_content="...")
result = await fix_markdown(ctx, request)
# 返回: fixed_content, changes_made, is_now_valid, remaining_errors
```

**自动修复的问题**:
- ✅ 添加缺失的必需部分
- ✅ 修复表格格式
- ✅ 添加区域标题（###）
- ✅ 添加状态标签

详细说明请查看 `FIX_MARKDOWN_GUIDE.md`

## 📋 Markdown格式要求

### 必需的三个部分

#### 1. Feature readiness status
```markdown
# Feature readiness status
| Sival       | Dev                     |                 |   |
|---------------|---------------------------------|---------------------|------|
|testable|testable|USB2 Host/Device Mode|
|na|testable|Compliance|
|testable|testable|Performance|notes, (rdar://123456)
```

**状态值**: `testable`, `issue`, `notready`, `na`, `blocked`

#### 2. Executive Summary
```markdown
# Executive Summary
- SiVal board with Linux (Thera_lws_release_20250502)
    - Feature Sweep completed
- Major issue
    - [HIGH] Critical bug description
        - Detailed information
        - rdar://153864687 (Bug title)
```

**支持**: 多级嵌套列表、状态标签、rdar链接

#### 3. Radar List
```markdown
# Radar List
### USB-IOT
- [LOW]
    - Completed
- [HIGH]
    - Critical issue description
        - Details
        - Impact analysis
```

**状态标签**: `[LOW]`, `[MEDIUM]`, `[MED-HIGH]`, `[HIGH]`, `[NOT STARTED]`

### rdar链接格式

支持两种格式，都会自动转换：

**新格式**:
```markdown
rdar://146598443 (Storage enumeration issue)
```
转换为: `Storage enumeration issue (rdar://146598443)`

**旧格式**:
```markdown
(rdar://146598443) 或 [rdar://146598443]
```
保持格式，添加超链接

## 🧪 测试

### 运行测试脚本

```bash
python3 test_simple.py
```

测试脚本会：
1. 读取示例Markdown文件
2. 模拟MCP的generate_report调用
3. 生成HTML报告
4. 与原始版本对比

### 测试结果

查看 `TEST_REPORT.md` 了解详细的测试结果和对比分析。

## ⚙️ 配置

### config.json

配置状态标签和功能状态的映射：

```json
{
  "status_mappings": {
    "HIGH": {"css_class": "sh", "priority": 1},
    "MEDIUM": {"css_class": "sm", "priority": 2},
    "LOW": {"css_class": "sl", "priority": 3}
  },
  "feature_status_mappings": {
    "testable": {"css_class": "testable"},
    "issue": {"css_class": "issue"},
    "notready": {"css_class": "notready"}
  }
}
```

### style.css

自定义HTML输出的样式。包含：
- 状态标签颜色（红色HIGH、黄色MEDIUM、绿色LOW）
- 表格样式
- 列表缩进
- 链接样式

## 📊 工作原理

```
Markdown输入
    ↓
format_check (可选)
    ↓
generate_report
    ↓
写入临时.md文件
    ↓
调用status_generator.py
    ├── 解析Markdown
    ├── 转换状态标签
    ├── 处理rdar链接
    ├── 生成HTML表格
    └── 应用CSS样式
    ↓
读取生成的HTML
    ↓
返回HTML内容
    ↓
清理临时文件
```

## ✅ 测试验证

根据 `TEST_REPORT.md`，MCP工具已通过完整测试：

- ✅ 所有核心功能正常工作
- ✅ 生成的HTML结构完整
- ✅ 状态标签正确应用
- ✅ rdar链接正确转换（新旧格式）
- ✅ 表格和列表正确生成
- ✅ CSS样式正确加载

**关键改进**: MCP版本修复了原始版本的rdar链接转换bug，现在能正确处理新格式 `rdar://123456 (title)`。

## 🔧 故障排除

### 问题: status_generator.py not found

**解决**: 确保 `status_generator.py` 在MCP目录中：
```bash
cp ../status_generator.py .
```

### 问题: config.json or style.css not found

**解决**: 复制配置文件到MCP目录：
```bash
cp ../config.json ../style.css .
```

### 问题: 生成的HTML格式不正确

**解决**: 
1. 使用 `format_check` 工具验证输入
2. 检查Markdown是否包含三个必需部分
3. 确保状态标签格式正确

## 📝 示例

完整的使用示例请参考 `test_simple.py`。

## 🤝 贡献

欢迎提交问题和改进建议。

## 📄 许可证

开源项目 - 可根据需要自由修改和分发。

## 🔗 相关文档

- `TEST_REPORT.md` - 详细测试报告
- `../HTML_DIFF_ANALYSIS.md` - HTML差异分析
- `../README.md` - 主项目文档

