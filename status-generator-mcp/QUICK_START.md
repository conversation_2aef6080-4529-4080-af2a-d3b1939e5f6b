# Status Generator MCP - 快速开始指南

## 🚀 5分钟快速上手

### 1. 检查文件

确保以下文件存在于 `status-generator-mcp/` 目录：

```bash
✅ server.py              # MCP服务器
✅ status_generator.py    # 转换引擎
✅ config.json            # 配置文件
✅ style.css              # CSS样式
```

### 2. 运行测试

```bash
cd status-generator-mcp

# 测试MCP功能
python3 test_simple.py

# 测试format_check
python3 test_format_check.py
```

预期结果：
```
✅ MCP工具基本功能正常
✅ 所有测试通过！format_check功能正常工作
```

### 3. 使用MCP工具

#### 方法1: 通过MCP服务器（推荐）

```bash
# 启动MCP服务器
python3 server.py
```

然后使用MCP客户端调用工具：
- `report_guide` - 获取格式指南
- `format_check` - 检查Markdown格式
- `generate_report` - 生成HTML报告

#### 方法2: 直接调用（快速测试）

```bash
# 直接生成报告
python3 status_generator.py --md input.md --html output.html

# 使用自定义配置和样式
python3 status_generator.py \
  --md input.md \
  --html output.html \
  --config config.json \
  --css style.css
```

## 📋 Markdown格式要求

### 必需的三个部分

```markdown
# Feature readiness status
| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|USB2 Host/Device Mode|

# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed
    - [HIGH] Critical issue

# Radar List
### USB-IOT
- LOW
    - Completed items
- HIGH
    - Critical issues
```

### 状态标签格式

支持两种格式：
- 带方括号: `[LOW]`, `[HIGH]`, `[MEDIUM]`, `[MED-HIGH]`, `[NOT STARTED]`
- 不带方括号: `LOW`, `HIGH`, `MEDIUM`, `MED-HIGH`, `NOT STARTED`

### rdar链接格式

支持两种格式，都会自动转换：
- 新格式: `rdar://146598443 (Bug title)` → `Bug title (rdar://146598443)`
- 旧格式: `(rdar://146598443)` → 保持格式，添加链接

## 🧪 测试用例

查看 `test_cases/` 目录中的10个示例文件：

**正确格式示例**:
- `valid_complete.md` - 完整正确的格式
- `valid_with_rdar_links.md` - 包含rdar链接
- `valid_with_nested_lists.md` - 深层嵌套列表

**错误格式示例**:
- `missing_feature_status.md` - 缺少必需部分
- `invalid_table_format.md` - 表格格式错误
- `missing_status_tags.md` - 缺少状态标签

## 📖 文档索引

| 文档 | 内容 | 适用场景 |
|------|------|----------|
| `QUICK_START.md` | 快速开始（本文件） | 新用户入门 |
| `README.md` | 完整项目文档 | 详细了解功能 |
| `TEST_REPORT.md` | MCP功能测试报告 | 了解测试结果 |
| `FORMAT_CHECK_TEST_REPORT.md` | 格式检查测试报告 | 了解格式验证 |
| `FINAL_SUMMARY.md` | 项目总结 | 全面了解项目 |

## ⚡ 常用命令

```bash
# 测试MCP功能
python3 test_simple.py

# 测试格式检查
python3 test_format_check.py

# 生成报告（使用实际文件）
python3 status_generator.py --md ../report.md --html output.html

# 查看生成的报告
open generated_report.html
```

## 🔍 故障排除

### 问题: ModuleNotFoundError: No module named 'fastmcp'

**解决**:
```bash
pip install fastmcp
```

### 问题: 找不到status_generator.py

**解决**:
```bash
# 确保在正确的目录
cd status-generator-mcp

# 检查文件是否存在
ls -l status_generator.py
```

### 问题: 生成的HTML格式不正确

**解决**:
```bash
# 1. 先检查格式
python3 -c "
from test_format_check import test_format_check_logic
with open('input.md', 'r') as f:
    result = test_format_check_logic(f.read())
    print(result)
"

# 2. 查看错误信息
# 3. 参考test_cases/中的正确示例
```

## 📊 预期输出

### 成功的测试输出

```
🧪 MCP工具简单测试
================================================================================
✅ 找到 status_generator.py
✅ 找到 config.json
✅ 找到 style.css
✅ 成功生成HTML: generated_report.html
   HTML大小: 17,206 字符
✅ MCP工具基本功能正常
```

### 成功的格式检查

```
📊 检查结果:
   is_valid: True
💡 建议 (1):
      1. Markdown format is valid.
✅ report.md 格式完全正确！
```

## 🎯 下一步

1. **阅读完整文档**: 查看 `README.md` 了解所有功能
2. **查看测试报告**: 阅读 `TEST_REPORT.md` 和 `FORMAT_CHECK_TEST_REPORT.md`
3. **尝试测试用例**: 使用 `test_cases/` 中的文件进行实验
4. **生成自己的报告**: 创建符合格式的Markdown文件并生成HTML

## 💡 提示

- 使用 `format_check` 在生成报告前验证格式
- 参考 `test_cases/` 中的示例文件
- 查看 `generated_report.html` 了解输出效果
- 阅读错误信息和建议来修复格式问题

## 📞 获取帮助

- 查看 `README.md` 获取详细文档
- 查看 `test_cases/` 获取格式示例
- 查看测试报告了解功能细节
- 运行测试脚本验证功能

---

**祝使用愉快！** 🎉

