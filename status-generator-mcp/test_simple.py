#!/usr/bin/env python3
"""
简单测试: 模拟MCP的generate_report调用
直接调用status_generator.py，不依赖fastmcp
"""

import os
import subprocess
from pathlib import Path

SCRIPT_DIR = Path(__file__).parent
PARENT_DIR = SCRIPT_DIR.parent

def read_file(filepath):
    """读取文件内容"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def simulate_mcp_generate_report(markdown_content):
    """模拟MCP的generate_report函数"""
    print("🔧 模拟MCP generate_report调用")
    print("=" * 80)
    
    # 定义路径（与server.py中的逻辑一致）
    STATUS_GENERATOR_PATH = SCRIPT_DIR / "status_generator.py"
    CONFIG_PATH = SCRIPT_DIR / "config.json"
    CSS_PATH = SCRIPT_DIR / "style.css"
    md_file_path = SCRIPT_DIR / "temp_report.md"
    html_file_path = SCRIPT_DIR / "generated_report.html"
    
    try:
        # 验证文件存在
        if not STATUS_GENERATOR_PATH.exists():
            print(f"❌ 错误: status_generator.py 不存在于 {STATUS_GENERATOR_PATH}")
            return None
        
        print(f"✅ 找到 status_generator.py")
        
        if CONFIG_PATH.exists():
            print(f"✅ 找到 config.json")
        else:
            print(f"⚠️  config.json 不存在，将使用默认配置")
        
        if CSS_PATH.exists():
            print(f"✅ 找到 style.css")
        else:
            print(f"⚠️  style.css 不存在，将使用默认样式")
        
        # 写入临时Markdown文件
        print(f"\n📝 写入Markdown到临时文件: {md_file_path}")
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        print(f"   内容大小: {len(markdown_content)} 字符")
        
        # 构建命令
        command = [
            "python3",
            str(STATUS_GENERATOR_PATH),
            "--md", str(md_file_path),
            "--html", str(html_file_path)
        ]
        
        if CONFIG_PATH.exists():
            command.extend(["--config", str(CONFIG_PATH)])
        if CSS_PATH.exists():
            command.extend(["--css", str(CSS_PATH)])
        
        print(f"\n🚀 执行命令:")
        print(f"   {' '.join(command)}")
        
        # 执行命令
        process = subprocess.run(command, capture_output=True, text=True, check=True)
        
        if process.stdout:
            print(f"\n📤 输出:\n{process.stdout}")
        if process.stderr:
            print(f"\n⚠️  警告:\n{process.stderr}")
        
        # 读取生成的HTML
        if not html_file_path.exists():
            print(f"❌ 错误: HTML文件未生成于 {html_file_path}")
            return None
        
        print(f"\n✅ 成功生成HTML: {html_file_path}")
        html_content = read_file(html_file_path)
        print(f"   HTML大小: {len(html_content):,} 字符")
        
        return html_content
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 执行错误:")
        print(f"   退出码: {e.returncode}")
        print(f"   Stdout: {e.stdout}")
        print(f"   Stderr: {e.stderr}")
        return None
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return None
    finally:
        # 清理临时Markdown文件
        if md_file_path.exists():
            md_file_path.unlink()
            print(f"\n🧹 清理临时文件: {md_file_path}")

def test_with_report_md():
    """使用report.md测试"""
    print("\n" + "=" * 80)
    print("测试: 使用 report.md 作为输入")
    print("=" * 80)
    
    report_md = PARENT_DIR / "report.md"
    if not report_md.exists():
        print(f"❌ 错误: {report_md} 不存在")
        return None
    
    print(f"📄 读取: {report_md}")
    markdown_content = read_file(report_md)
    print(f"   大小: {len(markdown_content)} 字符")
    
    # 模拟MCP调用
    html_mcp = simulate_mcp_generate_report(markdown_content)
    
    return html_mcp

def compare_with_original():
    """与原始生成的report.html对比"""
    print("\n" + "=" * 80)
    print("对比: MCP生成 vs 原始report.html")
    print("=" * 80)
    
    # 读取原始report.html
    original_html_path = PARENT_DIR / "report.html"
    if not original_html_path.exists():
        print(f"⚠️  原始report.html不存在，跳过对比")
        return
    
    original_html = read_file(original_html_path)
    print(f"📄 原始report.html: {len(original_html):,} 字符")
    
    # 读取MCP生成的HTML
    mcp_html_path = SCRIPT_DIR / "generated_report.html"
    if not mcp_html_path.exists():
        print(f"❌ MCP生成的HTML不存在")
        return
    
    mcp_html = read_file(mcp_html_path)
    print(f"📄 MCP生成的HTML: {len(mcp_html):,} 字符")
    
    # 对比
    print(f"\n📊 大小差异: {abs(len(original_html) - len(mcp_html))} 字符")
    
    if original_html == mcp_html:
        print("\n✅ 完全相同! MCP生成的报告与原始report.html完全一致")
        return True
    
    # 检查关键元素
    print("\n🔍 关键元素对比:")
    checks = {
        "表格数量": "<table",
        "rdar链接": '<a href="rdar://',
        "HIGH标签": '<span class="sh">',
        "MEDIUM标签": '<span class="sm">',
        "LOW标签": '<span class="sl">',
        "列表项": "<li class=",
    }
    
    all_match = True
    for name, pattern in checks.items():
        count_orig = original_html.count(pattern)
        count_mcp = mcp_html.count(pattern)
        match = "✅" if count_orig == count_mcp else "❌"
        print(f"   {match} {name}: 原始={count_orig}, MCP={count_mcp}")
        if count_orig != count_mcp:
            all_match = False
    
    if all_match:
        print("\n✅ 所有关键元素数量一致!")
        print("⚠️  虽然内容不完全相同，但结构和关键元素都正确")
    else:
        print("\n⚠️  发现关键元素数量差异")
    
    return all_match

def main():
    """主函数"""
    print("🧪 MCP工具简单测试")
    print("=" * 80)
    print("此测试模拟MCP的generate_report函数调用")
    print("直接调用status_generator.py，不依赖fastmcp模块")
    print("=" * 80)
    
    # 测试1: 使用report.md生成
    html_result = test_with_report_md()
    
    if html_result is None:
        print("\n❌ 测试失败: 无法生成HTML")
        return 1
    
    # 测试2: 与原始report.html对比
    compare_result = compare_with_original()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    if html_result:
        print("✅ MCP工具基本功能正常")
        print(f"   - 成功调用status_generator.py")
        print(f"   - 成功生成HTML报告")
        print(f"   - 输出文件: status-generator-mcp/generated_report.html")
        
        if compare_result:
            print("\n✅ 生成的报告与原始版本一致!")
        else:
            print("\n⚠️  生成的报告与原始版本有细微差异")
            print("   这可能是正常的，因为:")
            print("   - 时间戳不同")
            print("   - 文件路径不同")
            print("   - 格式化细节不同")
        
        return 0
    else:
        print("❌ 测试失败")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())

