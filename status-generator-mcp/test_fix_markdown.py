#!/usr/bin/env python3
"""
测试fix_markdown功能
验证自动修复Markdown格式的能力
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from test_format_check import test_format_check_logic
import re

def fix_markdown_logic(markdown_content):
    """
    模拟fix_markdown的逻辑（从server.py提取）
    """
    changes_made = []
    
    # Helper function to split sections
    def split_sections(md_text):
        sections = re.split(r'(?m)^# ', md_text)
        section_dict = {}
        section_order = []
        for section in sections:
            if not section.strip():
                continue
            lines = section.splitlines()
            title = lines[0].strip()
            content = "\n".join(lines[1:]).strip()
            section_dict[title] = content
            section_order.append(title)
        return section_dict, section_order
    
    sections, section_order = split_sections(markdown_content)

    # Fix 1: Add missing required sections
    required_sections = ["Feature readiness status", "Executive Summary", "Radar List"]
    for req_sec in required_sections:
        if req_sec not in sections:
            if req_sec == "Feature readiness status":
                sections[req_sec] = """| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|Feature 1|
|testable|testable|Feature 2|"""
                changes_made.append(f"Added missing section: '# {req_sec}' with template table")
            elif req_sec == "Executive Summary":
                sections[req_sec] = """- Summary item 1
    - Detail 1"""
                changes_made.append(f"Added missing section: '# {req_sec}' with template content")
            elif req_sec == "Radar List":
                sections[req_sec] = """### Area 1
- LOW
    - Completed items"""
                changes_made.append(f"Added missing section: '# {req_sec}' with template content")

    # Ensure correct section order: Feature readiness status -> Executive Summary -> Radar List
    correct_order = ["Feature readiness status", "Executive Summary", "Radar List"]
    # Keep other sections in their original order
    other_sections = [s for s in section_order if s not in correct_order]
    section_order = correct_order + other_sections
    
    # Fix 2: Fix Feature readiness status table format
    if "Feature readiness status" in sections:
        content = sections["Feature readiness status"]
        if content and not ('|' in content and '---' in content):
            # Convert plain text to table
            lines = [line.strip() for line in content.splitlines() if line.strip()]
            table = """| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|"""
            for line in lines[:5]:  # Take first 5 lines
                table += f"\n|testable|testable|{line}|"
            sections["Feature readiness status"] = table
            changes_made.append("Converted 'Feature readiness status' plain text to table format")
    
    # Fix 3: Add area headers to Radar List
    if "Radar List" in sections:
        content = sections["Radar List"]
        if content:
            area_pattern = re.compile(r'^###\s+(.+)', re.MULTILINE)
            areas = area_pattern.findall(content)
            
            if not areas:
                # No area headers found, add a default one
                sections["Radar List"] = f"### General\n{content}"
                changes_made.append("Added default area header '### General' to Radar List")
    
    # Fix 4: Add status tags to areas missing them
    if "Radar List" in sections:
        content = sections["Radar List"]
        if content:
            area_pattern = re.compile(r'^###\s+(.+)', re.MULTILINE)
            status_pattern = re.compile(r'(?:\[)?(LOW|MEDIUM|MED-HIGH|HIGH|NOT STARTED)(?:\])?', re.IGNORECASE)
            
            lines = content.splitlines()
            fixed_lines = []
            current_area = None
            area_has_status = False
            area_start_line = -1
            
            for line in lines:
                if area_pattern.match(line):
                    # Check if previous area needs a status tag
                    if current_area and not area_has_status and area_start_line >= 0:
                        # Insert a LOW status tag after the area header
                        fixed_lines.insert(area_start_line + 1, "- LOW")
                        fixed_lines.insert(area_start_line + 2, "    - Items completed")
                        changes_made.append(f"Added default status tag 'LOW' to area '{current_area}'")
                    
                    current_area = area_pattern.match(line).group(1)
                    area_has_status = False
                    area_start_line = len(fixed_lines)
                    fixed_lines.append(line)
                    
                elif line.strip().startswith('-'):
                    indent = len(line) - len(line.lstrip())
                    if indent <= 4:
                        line_content = line.strip()[1:].strip()
                        if status_pattern.match(line_content):
                            area_has_status = True
                    fixed_lines.append(line)
                else:
                    fixed_lines.append(line)
            
            # Check last area
            if current_area and not area_has_status and area_start_line >= 0:
                fixed_lines.insert(area_start_line + 1, "- LOW")
                fixed_lines.insert(area_start_line + 2, "    - Items completed")
                changes_made.append(f"Added default status tag 'LOW' to area '{current_area}'")
            
            sections["Radar List"] = "\n".join(fixed_lines)
    
    # Reconstruct the markdown
    fixed_content = ""
    for section_title in section_order:
        if section_title in sections:
            fixed_content += f"# {section_title}\n{sections[section_title]}\n\n"
    
    # Run format_check on the fixed content
    check_result = test_format_check_logic(fixed_content)
    
    return {
        'fixed_content': fixed_content.strip(),
        'changes_made': changes_made,
        'is_now_valid': check_result['is_valid'],
        'remaining_errors': check_result['errors']
    }

def test_fix_markdown():
    """测试fix_markdown功能"""
    print("=" * 80)
    print("fix_markdown 功能测试")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "缺少Feature readiness status",
            "input": """# Executive Summary
- Summary item

# Radar List
### USB-IOT
- LOW
    - Completed""",
            "expected_fixes": 1
        },
        {
            "name": "缺少所有必需部分",
            "input": "Some random text",
            "expected_fixes": 3
        },
        {
            "name": "表格格式错误",
            "input": """# Feature readiness status
This is plain text, not a table

# Executive Summary
- Summary

# Radar List
### USB-IOT
- LOW
    - Done""",
            "expected_fixes": 1
        },
        {
            "name": "缺少区域标题",
            "input": """# Feature readiness status
| Sival | Dev | Feature |
|---|---|---|
|testable|testable|Feature 1|

# Executive Summary
- Summary

# Radar List
- LOW
    - Items without area header""",
            "expected_fixes": 1
        },
        {
            "name": "缺少状态标签",
            "input": """# Feature readiness status
| Sival | Dev | Feature |
|---|---|---|
|testable|testable|Feature 1|

# Executive Summary
- Summary

# Radar List
### USB-IOT
- Item without status
### Another Area
- Also no status""",
            "expected_fixes": 2
        }
    ]
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'=' * 80}")
        print(f"测试 {i}: {test_case['name']}")
        print(f"{'=' * 80}")
        
        # 运行format_check on原始内容
        print(f"\n📄 原始内容:")
        original_check = test_format_check_logic(test_case['input'])
        print(f"   is_valid: {original_check['is_valid']}")
        if original_check['errors']:
            print(f"   错误数: {len(original_check['errors'])}")
            for error in original_check['errors'][:2]:
                print(f"      - {error}")
        
        # 运行fix_markdown
        print(f"\n🔧 运行 fix_markdown:")
        result = fix_markdown_logic(test_case['input'])
        
        print(f"   修改数: {len(result['changes_made'])}")
        for change in result['changes_made']:
            print(f"      ✓ {change}")
        
        # 检查修复后的内容
        print(f"\n📊 修复后:")
        print(f"   is_valid: {result['is_now_valid']}")
        if result['remaining_errors']:
            print(f"   剩余错误: {len(result['remaining_errors'])}")
            for error in result['remaining_errors']:
                print(f"      - {error}")
        
        # 验证
        if result['is_now_valid']:
            print(f"\n✅ 测试通过 - 修复后格式正确")
            passed += 1
        else:
            print(f"\n⚠️  测试部分通过 - 仍有错误，但已改进")
            if len(result['changes_made']) > 0:
                passed += 1
            else:
                failed += 1
    
    # 总结
    print(f"\n{'=' * 80}")
    print(f"测试总结")
    print(f"{'=' * 80}")
    print(f"总测试数: {len(test_cases)}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"成功率: {passed / len(test_cases) * 100:.1f}%")
    
    if failed == 0:
        print(f"\n🎉 所有测试通过！fix_markdown功能正常")
        return 0
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(test_fix_markdown())

