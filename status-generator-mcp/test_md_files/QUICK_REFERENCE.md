# 测试文件快速参考

## 🚀 快速开始

```bash
# 运行所有测试
cd status-generator-mcp
python3 test_mcp_with_files.py

# 预期结果：所有测试通过 ✅
```

## 📁 测试文件速查

### ✅ 正确格式（用于成功测试）

| 文件 | 用途 | 特点 |
|------|------|------|
| `01_valid_complete.md` | 基本功能测试 | 完整的三部分结构 |
| `08_valid_with_rdar_links.md` | rdar链接测试 | 新旧两种格式 |
| `09_valid_with_nested_lists.md` | 嵌套列表测试 | 4级嵌套 |
| `10_empty_executive_summary.md` | 边界测试 | 空部分处理 |

### ❌ 错误格式（用于错误检测测试）

| 文件 | 测试目标 | 预期错误 |
|------|----------|----------|
| `02_missing_feature_status.md` | 必需部分检测 | Missing Feature readiness status |
| `03_missing_executive_summary.md` | 必需部分检测 | Missing Executive Summary |
| `04_missing_radar_list.md` | 必需部分检测 | Missing Radar List |
| `05_invalid_table_format.md` | 表格验证 | Should contain table |
| `06_missing_area_headers.md` | 区域标题检测 | Should have ### headers |
| `07_missing_status_tags.md` | 状态标签验证 | Has no status tags |

## 🧪 常用测试命令

### 测试单个文件的format_check

```bash
python3 << 'EOF'
from test_format_check import test_format_check_logic
with open('test_md_files/01_valid_complete.md', 'r') as f:
    result = test_format_check_logic(f.read())
print(f"Valid: {result['is_valid']}")
if result['errors']:
    for e in result['errors']:
        print(f"  Error: {e}")
EOF
```

### 生成单个文件的HTML

```bash
python3 status_generator.py \
  --md test_md_files/01_valid_complete.md \
  --html test_outputs/test.html
```

### 查看生成的HTML

```bash
# 在浏览器中打开
open test_outputs/01_valid_complete.html

# 查看文件列表
ls -lh test_outputs/
```

## 📊 预期结果

### format_check 测试

```
✅ 正确文件 (4个): is_valid=True
❌ 错误文件 (6个): is_valid=False, 包含错误信息
```

### generate_report 测试

```
✅ 正确文件 (4个): 成功生成HTML (11-13KB)
⏭️ 错误文件 (6个): 跳过生成
```

## 🔍 验证要点

### rdar链接转换（文件08）

**输入**:
```markdown
rdar://153864687 (Sonar: Only Resource ID in CSV import)
```

**输出**:
```html
Sonar: Only Resource ID in CSV import (<a href="rdar://153864687">rdar://153864687</a>)
```

### 嵌套列表（文件09）

**输入**:
```markdown
- Level 1
    - Level 2
        - Level 3
            - Level 4
```

**输出**:
```html
<li class="level-0">Level 1</li>
<li class="level-1">Level 2</li>
<li class="level-2">Level 3</li>
<li class="level-3">Level 4</li>
```

### 错误检测（文件02-07）

每个错误文件都应该：
- ❌ format_check返回`is_valid=False`
- 📝 包含清晰的错误信息
- 💡 提供修复建议
- ⏭️ 不尝试生成HTML

## 💡 使用技巧

1. **学习正确格式** - 查看01, 08, 09, 10文件
2. **理解错误类型** - 查看02-07文件
3. **验证修复** - 修改错误文件后重新测试
4. **对比输出** - 比较不同文件生成的HTML

## 📖 相关文档

- `README.md` - 详细的测试文件说明
- `../TEST_FILES_SUMMARY.md` - 完整的测试结果报告
- `../test_mcp_with_files.py` - 测试脚本源码

---

**快速参考完成！开始测试吧！** 🚀

