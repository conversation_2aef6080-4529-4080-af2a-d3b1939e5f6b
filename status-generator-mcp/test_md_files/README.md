# MCP Server 测试文件

这个目录包含10个Markdown测试文件，用于测试MCP Server的`format_check`和`generate_report`功能。

## 📁 测试文件列表

### ✅ 正确格式文件（4个）

这些文件应该通过format_check并成功生成HTML：

| 文件 | 说明 | 特点 |
|------|------|------|
| `01_valid_complete.md` | 完整正确的格式 | 包含所有必需部分，格式完全正确 |
| `08_valid_with_rdar_links.md` | 包含rdar链接 | 测试新旧两种rdar链接格式 |
| `09_valid_with_nested_lists.md` | 深层嵌套列表 | 测试4级嵌套列表处理 |
| `10_empty_executive_summary.md` | 空的Executive Summary | 测试空部分的处理 |

### ❌ 错误格式文件（6个）

这些文件应该被format_check检测出错误：

| 文件 | 说明 | 预期错误 |
|------|------|----------|
| `02_missing_feature_status.md` | 缺少Feature readiness status | Missing required section |
| `03_missing_executive_summary.md` | 缺少Executive Summary | Missing required section |
| `04_missing_radar_list.md` | 缺少Radar List | Missing required section |
| `05_invalid_table_format.md` | 表格格式错误 | Should contain a Markdown table |
| `06_missing_area_headers.md` | 缺少区域标题 | Should have area headers (###) |
| `07_missing_status_tags.md` | 缺少状态标签 | Has no status tags |

## 🧪 使用方法

### 方法1: 批量测试（推荐）

使用测试脚本批量测试所有文件：

```bash
cd status-generator-mcp
python3 test_mcp_with_files.py
```

预期输出：
```
📊 format_check 测试:
   总数: 10
   ✅ 通过: 10
   ❌ 失败: 0
   成功率: 100.0%

📊 generate_report 测试:
   总数: 4
   ✅ 通过: 4
   ❌ 失败: 0
   成功率: 100.0%

🎉 所有测试通过！MCP Server功能正常
```

### 方法2: 单个文件测试

测试单个文件的format_check：

```bash
python3 << 'EOF'
import sys
sys.path.insert(0, '/Users/<USER>/Documents/status generator/status-generator-mcp')

from test_format_check import test_format_check_logic

# 读取测试文件
with open('test_md_files/01_valid_complete.md', 'r') as f:
    content = f.read()

# 运行format_check
result = test_format_check_logic(content)

print(f"is_valid: {result['is_valid']}")
if result['errors']:
    print("Errors:")
    for error in result['errors']:
        print(f"  - {error}")
EOF
```

生成单个文件的HTML：

```bash
python3 status_generator.py \
  --md test_md_files/01_valid_complete.md \
  --html test_outputs/01_valid_complete.html
```

### 方法3: 通过MCP Server测试

如果MCP Server正在运行，可以通过MCP客户端调用：

```python
# 使用MCP客户端
import asyncio
from fastmcp import Context

# 读取测试文件
with open('test_md_files/01_valid_complete.md', 'r') as f:
    markdown_content = f.read()

# 调用format_check
from server import format_check, FormatCheckRequest
ctx = Context()
request = FormatCheckRequest(markdown_content=markdown_content)
result = await format_check(ctx, request)

print(f"is_valid: {result.is_valid}")
print(f"errors: {result.errors}")
print(f"suggestions: {result.suggestions}")

# 如果格式正确，生成报告
if result.is_valid:
    from server import generate_report, GenerateReportRequest
    request = GenerateReportRequest(markdown_content=markdown_content)
    report = await generate_report(ctx, request)
    print(f"Generated HTML: {len(report.html_report)} characters")
```

## 📊 测试覆盖

### format_check 功能测试

| 功能 | 测试文件 | 状态 |
|------|----------|------|
| 检测缺失的必需部分 | 02, 03, 04 | ✅ |
| 验证表格格式 | 05 | ✅ |
| 检测区域标题 | 06 | ✅ |
| 验证状态标签 | 07 | ✅ |
| 支持rdar链接 | 08 | ✅ |
| 处理嵌套列表 | 09 | ✅ |
| 处理空部分 | 10 | ✅ |

### generate_report 功能测试

| 功能 | 测试文件 | 状态 |
|------|----------|------|
| 基本HTML生成 | 01 | ✅ |
| rdar链接转换 | 08 | ✅ |
| 嵌套列表处理 | 09 | ✅ |
| 空部分处理 | 10 | ✅ |

## 📁 输出文件

运行`test_mcp_with_files.py`后，生成的HTML文件将保存在`test_outputs/`目录：

```
test_outputs/
├── 01_valid_complete.html
├── 08_valid_with_rdar_links.html
├── 09_valid_with_nested_lists.html
└── 10_empty_executive_summary.html
```

## 🔍 查看结果

### 查看生成的HTML

```bash
# 在浏览器中打开
open test_outputs/01_valid_complete.html

# 或查看文件大小
ls -lh test_outputs/
```

### 对比不同文件的输出

```bash
# 对比两个HTML文件
diff test_outputs/01_valid_complete.html test_outputs/08_valid_with_rdar_links.html
```

## 💡 使用建议

1. **开发时测试** - 修改server.py后运行测试验证功能
2. **回归测试** - 定期运行确保功能稳定
3. **学习参考** - 查看测试文件了解正确格式
4. **调试工具** - 使用错误文件测试错误处理

## 🎯 预期结果

### 正确文件（应该通过）

- ✅ format_check返回`is_valid=True`
- ✅ generate_report成功生成HTML
- ✅ HTML文件大小在15-20KB之间
- ✅ 包含正确的表格、列表和样式

### 错误文件（应该失败）

- ❌ format_check返回`is_valid=False`
- ❌ 包含清晰的错误信息
- ❌ 提供有用的修复建议
- ⏭️ 不尝试生成HTML（因为格式不正确）

## 📝 添加新测试

要添加新的测试文件：

1. 在此目录创建新的`.md`文件
2. 在`test_mcp_with_files.py`的`TEST_CASES`字典中添加配置
3. 运行测试验证

示例：
```python
"11_new_test.md": {
    "name": "✅ 新测试用例",
    "should_pass_format_check": True,
    "should_generate_html": True
}
```

## 🔗 相关文档

- `../README.md` - MCP Server完整文档
- `../FORMAT_CHECK_TEST_REPORT.md` - format_check详细测试报告
- `../TEST_REPORT.md` - MCP功能测试报告
- `../test_format_check.py` - format_check测试逻辑

---

**这些测试文件确保MCP Server的功能正确可靠！** ✅

