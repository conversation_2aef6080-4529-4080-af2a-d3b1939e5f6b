#!/usr/bin/env python3

import json
import os
import logging
import re
from fastmcp import FastMCP, Context
from fastmcp.exceptions import ToolError
from pydantic import BaseModel, Field
from typing import List, Annotated, Optional, Tuple, Dict
import subprocess
import tempfile
from pathlib import Path

# Get the absolute path of the script's directory
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
STATUS_GENERATOR_PATH = os.path.join(SCRIPT_DIR, 'status_generator.py')
CONFIG_PATH = os.path.join(SCRIPT_DIR, 'config.json')
CSS_PATH = os.path.join(SCRIPT_DIR, 'style.css')

def setup_logging():
    """
    Sets up logging to both console and file.
    """
    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # File handler
    file_handler = logging.FileHandler('mcp_conversation.log')
    file_handler.setFormatter(log_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_formatter)
    
    # Get root logger
    root_logger = logging.getLogger()
    if not root_logger.hasHandlers():
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

# Call the setup function
setup_logging()

mcp_app = FastMCP(
    name="status-generator-mcp",
    instructions=(
        "A server for generating status reports from Markdown input.\n\n"
        "Usage Guide:\n"
        "1. Use the `report_guide` tool to understand the expected Markdown format.\n"
        "2. Use the `fix_markdown` tool to automatically fix common format issues.\n"
        "2. Use the `format_check` tool to validate your Markdown input.\n"
        "3. Use the `generate_report` tool to convert your Markdown into an HTML report."
    ),
    mask_error_details=True,
)

class ReportGuideResponse(BaseModel):
    section: str
    description: str
    example: str

@mcp_app.tool()
async def report_guide(ctx: Context) -> List[ReportGuideResponse]:
    """
    Provides a guide to the expected Markdown format for status reports, including descriptions and examples for each section.
    """
    await ctx.info("Providing report format guide.")
    guide_data = [
        ReportGuideResponse(
            section="Feature readiness status",
            description="This section should be a Markdown table summarizing the readiness status of various features. Each row represents a feature, and columns typically include 'Sival', 'Dev', feature name, and optional notes. Statuses: 'testable', 'issue', 'notready', 'na', 'blocked'. The table can have 3-4 columns. rdar links in notes are supported.",
            example="""```markdown
# Feature readiness status
| Sival       | Dev                     |                 |   |
|---------------|---------------------------------|---------------------|------|
|testable|testable|USB2 Host/Device Mode|
|testable|testable|MultiFunction|
|testable|testable|Sleep/Wake|
|na|testable|Reboot / ColdBoot|test radar,(rdar://132552351)
|na|testable|Compliance|
|testable|testable|Performance|
|testable|testable|Stress|
```"""
        ),
        ReportGuideResponse(
            section="Executive Summary",
            description="This section provides a high-level overview of the report. It typically uses bullet points to list key achievements, issues, or updates. Nested bullet points are supported. rdar links should be in the format `rdar://123456789 (Radar Title)` or `Radar Title (rdar://123456789)`.",
            example="""```markdown
# Executive Summary
- SiVal board with Linux (Linux_release_20250502)
	- Feature-Sweep, in progress (70%), ETC 5/30
- Dev board with iOS (Luck23A254c)
	- IOT test, Completed
	- Compliance Test, not started
	- Stress Test, in progress (60%), ETC 6/30
	- Performance Test, Completed
- Main Issue
	- [Dev][SW regression] system panic when plug cruzer blade (panic: Kernel tag check fault)[rdar://141270725]
		- Verified with PRkit
```"""
        ),
        ReportGuideResponse(
            section="Radar List",
            description="This section details specific issues or radars. It uses `###` for area titles, followed by bullet points for status and summary. Statuses should be enclosed in square brackets, e.g., `[LOW]`, `[HIGH]`, `[MED-HIGH]`, `[NOT STARTED]`. Direction indicators `[up]` or `[down]` can be included before the status. Nested bullet points are used for summaries. rdar links should be in the format `rdar://123456789 (Radar Title)` or `Radar Title (rdar://123456789)`.",
            example="""```markdown
# Radar List
### USB-IOT
- [HIGH][up]
    - [Dev]Storage with ASMedia chipset (ASM235CM) is enumerated as SS+ when plugged. (SW WA doesn't work)) (rdar://132552351)
        - Verity when SW WA landed
- [LOW]
    - Completed
```"""
        ),
    ]
    return guide_data

class FormatCheckRequest(BaseModel):
    markdown_content: Annotated[str, Field(description="The Markdown content to be checked for format compliance.")]

class FormatCheckResponse(BaseModel):
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)

@mcp_app.tool()
async def format_check(ctx: Context, request: FormatCheckRequest) -> FormatCheckResponse:
    """
    Checks the provided Markdown content for compliance with the expected status report format.
    Returns a list of errors and suggestions if the format is incorrect.
    """
    await ctx.info("Checking Markdown content format.")
    markdown_content = request.markdown_content
    errors = []
    suggestions = []

    try:
        # Helper to split sections
        def split_sections(md_text: str) -> Dict[str, str]:
            sections = re.split(r'(?m)^# ', md_text)
            section_dict = {}
            for section in sections:
                if not section.strip():
                    continue
                lines = section.splitlines()
                title = lines[0].strip()
                content = "\n".join(lines[1:]).strip()
                section_dict[title] = content
            return section_dict

        sections = split_sections(markdown_content)

        # Check for required sections
        required_sections = ["Feature readiness status", "Executive Summary", "Radar List"]
        for req_sec in required_sections:
            if req_sec not in sections:
                errors.append(f"Missing required section: '# {req_sec}'")
                suggestions.append(f"Please add a top-level header '# {req_sec}' to your report.")

        # Validate "Feature readiness status"
        if "Feature readiness status" in sections:
            content = sections["Feature readiness status"]
            if not content.strip().startswith('|'):
                errors.append("Feature readiness status section must start with a Markdown table.")
                suggestions.append("Ensure the 'Feature readiness status' section is a valid Markdown table with a header and separator line.")
            else:
                # Basic table structure check
                lines = content.splitlines()
                if len(lines) < 2 or not lines[1].strip().startswith('|---'):
                    errors.append("Feature readiness status table is missing the header separator line (e.g., `|---|---|---|`).")
                    suggestions.append("Add a separator line like `|---|---|---|` after the table header in 'Feature readiness status'.")
                
                # Check for valid feature statuses (simplified check)
                valid_feature_statuses = ['testable', 'issue', 'notready', 'na', 'blocked']
                for line in lines[2:]: # Skip header and separator
                    cells = [c.strip() for c in line.split('|') if c.strip()]
                    if len(cells) >= 2: # Assuming at least two status columns
                        sival_status = cells[0].lower()
                        dev_status = cells[1].lower()
                        if sival_status not in valid_feature_statuses:
                            errors.append(f"Invalid 'Sival' status '{sival_status}' in 'Feature readiness status'. Expected one of: {', '.join(valid_feature_statuses)}.")
                        if dev_status not in valid_feature_statuses:
                            errors.append(f"Invalid 'Dev' status '{dev_status}' in 'Feature readiness status'. Expected one of: {', '.join(valid_feature_statuses)}.")


        # Validate "Executive Summary"
        if "Executive Summary" in sections:
            content = sections["Executive Summary"]
            if not content.strip().startswith('-'):
                errors.append("Executive Summary section should primarily use bullet points.")
                suggestions.append("Start items in 'Executive Summary' with a hyphen (`-`) for bullet points.")
            
            # Check rdar link format (removed strict checking, generator handles conversion)
            rdar_pattern_new = re.compile(r'(rdar://\d+)\s*\(([^)]+)\)')
            rdar_pattern_old = re.compile(r'[\[\(](rdar://[^\]\)]+?)[\]\)]')
            for line in content.splitlines():
                if rdar_pattern_new.search(line) or rdar_pattern_old.search(line):
                    pass


        # Validate "Radar List"
        if "Radar List" in sections:
            content = sections["Radar List"]
            area_pattern = re.compile(r'###\s+(.+)')
            # 支持两种格式: [LOW] 或 LOW (带或不带方括号)
            status_pattern = re.compile(r'(?:\[)?(LOW|MEDIUM|MED-HIGH|HIGH|NOT STARTED)(?:\])?', re.IGNORECASE)

            lines = content.splitlines()
            areas_found = area_pattern.findall(content)

            if not areas_found:
                errors.append("'Radar List' section should have area headers (###).")
                suggestions.append("Add area headers like '### USB-IOT' in the 'Radar List' section.")

            # 检查每个区域是否至少有一个状态标签
            current_area = None
            area_has_status = False

            for line in lines:
                if area_pattern.match(line):
                    # 检查上一个区域
                    if current_area and not area_has_status:
                        errors.append(f"Area '{current_area}' in 'Radar List' has no status tags.")
                        suggestions.append(f"Add at least one status tag like `[LOW]`, `[HIGH]` under area '{current_area}'.")

                    current_area = area_pattern.match(line).group(1)
                    area_has_status = False

                elif line.strip().startswith('-'):
                    # 检查是否是顶级列表项（无缩进或最小缩进）
                    indent = len(line) - len(line.lstrip())
                    if indent <= 4:  # 顶级或第一级缩进
                        # 检查这一行或其内容是否包含状态
                        line_content = line.strip()[1:].strip()  # 移除 '-' 和空格
                        if status_pattern.match(line_content):
                            area_has_status = True

            # 检查最后一个区域
            if current_area and not area_has_status:
                errors.append(f"Area '{current_area}' in 'Radar List' has no status tags.")
                suggestions.append(f"Add at least one status tag like `[LOW]`, `[HIGH]` under area '{current_area}'.")

    except Exception as e:
        errors.append(f"An unexpected error occurred during format checking: {e}")
        suggestions.append("Please review your Markdown content for any syntax errors or unexpected characters.")

    if errors:
        return FormatCheckResponse(is_valid=False, errors=errors, suggestions=suggestions)
    else:
        return FormatCheckResponse(is_valid=True, errors=[], suggestions=["Markdown format is valid."])
class GenerateReportRequest(BaseModel):
    markdown_content: Annotated[str, Field(description="The Markdown content to be converted into an HTML report. If not provided or empty, a default template will be used.")]
    use_default_template: Annotated[bool, Field(default=False, description="If True, generates a report using default features and radar areas instead of provided markdown_content.")]
    default_features: Annotated[Optional[List[str]], Field(default=None, description="List of default features to include. If not specified, uses common USB-C/Thunderbolt features.")]
    default_radar_areas: Annotated[Optional[List[str]], Field(default=None, description="List of default radar areas to include. If not specified, uses common areas like USB-IOT, Compliance, Performance.")]

class GenerateReportResponse(BaseModel):
    html_report: str = Field(description="The generated HTML report content.")
    message: str = Field(description="Status message about the report generation.")
    used_template: bool = Field(description="Whether a default template was used instead of provided content.")


def _generate_default_template(custom_features: Optional[List[str]] = None, custom_radar_areas: Optional[List[str]] = None) -> str:
    """Generate a default Markdown template with standard features and radar areas."""

    # Default features for USB-C/Thunderbolt development
    default_features = custom_features or [
        "USB-C Power Delivery",
        "DisplayPort Alt Mode",
        "USB 3.2 Gen 2",
        "Thunderbolt 4",
        "Power Management",
        "Sleep/Wake",
        "Compliance Testing",
        "Performance Validation"
    ]

    # Default radar areas
    default_areas = custom_radar_areas or [
        "USB-IOT",
        "CIO-IOT",
        "Stress",
        "Compliance",
        "Performance",
        "SoC Depth"
    ]

    # Generate Feature readiness status table
    feature_table = "# Feature readiness status\n"
    feature_table += "| Sival | Dev | Feature | Notes |\n"
    feature_table += "|-------|-----|---------|-------|\n"

    for feature in default_features:
        feature_table += f"| testable | testable | {feature} | |\n"

    # Generate Executive Summary
    executive_summary = """# Executive Summary
- All core features have been tested and are stable
    - Feature validation completed across all major components
    - Integration testing shows good compatibility
- System performance meets requirements
    - Power efficiency within expected parameters
    - Thermal management operating normally
- No critical issues identified in current testing cycle
    - Minor optimizations identified for future releases
    - All blocking issues have been resolved"""

    # Generate Radar List
    radar_list = "# Radar List\n"

    import random

    for i, area in enumerate(default_areas):
        # Assign mostly LOW status with occasional MEDIUM
        status = "LOW" if i < len(default_areas) - 2 else random.choice(["LOW", "MEDIUM"])

        radar_list += f"### {area}\n"
        radar_list += f"- {status}\n"

        if status == "LOW":
            radar_list += f"    - All {area.lower()} features are stable and performing as expected\n"
            radar_list += f"    - Minor optimizations being explored for future updates\n"
        else:
            radar_list += f"    - Some {area.lower()} issues under investigation\n"
            radar_list += f"    - Working on resolution with expected completion soon\n"

        radar_list += "\n"

    # Combine all sections
    template = f"{feature_table}\n{executive_summary}\n\n{radar_list}"

    return template


@mcp_app.tool()
async def generate_report(ctx: Context, request: GenerateReportRequest) -> GenerateReportResponse:
    """
    Generates an HTML status report from the provided Markdown content using the status_generator.py script.

    If markdown_content is empty or use_default_template is True, generates a default template with:
    - Common USB-C/Thunderbolt features in Feature readiness status
    - Standard executive summary
    - Default radar areas (USB-IOT, Compliance, Performance, etc.)

    The Markdown content must follow the format specified in report_guide with three required sections:
    1. # Feature readiness status (table format)
    2. # Executive Summary (bullet points with nested lists)
    3. # Radar List (### area headers with status tags and bullet points)
    """
    await ctx.info("Generating HTML report using status_generator.py")

    # Determine if we should use default template
    use_template = request.use_default_template or not request.markdown_content.strip()

    if use_template:
        await ctx.info("Using default template with standard features and radar areas")
        markdown_content = _generate_default_template(request.default_features, request.default_radar_areas)
    else:
        markdown_content = request.markdown_content

    # Define paths
    output_dir = Path(SCRIPT_DIR)
    md_file_path = output_dir / "temp_report.md"
    html_file_path = output_dir / "generated_report.html"

    try:
        # Validate that required files exist
        if not os.path.exists(STATUS_GENERATOR_PATH):
            raise ToolError(f"status_generator.py not found at {STATUS_GENERATOR_PATH}")
        if not os.path.exists(CONFIG_PATH):
            await ctx.warning(f"config.json not found at {CONFIG_PATH}, using defaults")
        if not os.path.exists(CSS_PATH):
            await ctx.warning(f"style.css not found at {CSS_PATH}, using defaults")

        # Write markdown content to a temporary file
        await ctx.info(f"Writing Markdown content to {md_file_path}")
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        # Construct the command to run status_generator.py
        command = [
            "python3",
            STATUS_GENERATOR_PATH,
            "--md", str(md_file_path),
            "--html", str(html_file_path)
        ]

        # Add optional config and css if they exist
        if os.path.exists(CONFIG_PATH):
            command.extend(["--config", CONFIG_PATH])
        if os.path.exists(CSS_PATH):
            command.extend(["--css", CSS_PATH])

        await ctx.info(f"Executing command: {' '.join(command)}")

        # Execute the command
        process = subprocess.run(command, capture_output=True, text=True, check=True)

        if process.stdout:
            await ctx.info(f"status_generator.py output:\n{process.stdout}")
        if process.stderr:
            await ctx.warning(f"status_generator.py warnings:\n{process.stderr}")

        # Read the generated HTML report
        if not os.path.exists(html_file_path):
            raise ToolError(f"HTML file was not generated at {html_file_path}")

        await ctx.info(f"Reading generated HTML from {html_file_path}")
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_report_content = f.read()

        await ctx.info(f"Successfully generated HTML report ({len(html_report_content)} characters)")

        return GenerateReportResponse(
            html_report=html_report_content,
            message=f"Successfully generated HTML report using status_generator.py. Output size: {len(html_report_content)} characters.",
            used_template=use_template
        )

    except subprocess.CalledProcessError as e:
        error_message = f"Error running status_generator.py. Exit code: {e.returncode}\nStdout: {e.stdout}\nStderr: {e.stderr}"
        await ctx.error(error_message)
        raise ToolError(f"Failed to generate report: {error_message}")
    except FileNotFoundError as e:
        raise ToolError(f"File not found: {e}")
    except Exception as e:
        await ctx.error(f"An unexpected error occurred during report generation: {e}")
        raise ToolError(f"An unexpected error occurred during report generation: {e}")
    finally:
        # Clean up the temporary markdown file
        if md_file_path.exists():
            md_file_path.unlink()
            await ctx.info(f"Cleaned up temporary file: {md_file_path}")


# ============================================================================
# Tool 4: fix_markdown - Automatically fix common Markdown format issues
# ============================================================================

class FixMarkdownRequest(BaseModel):
    markdown_content: str = Field(
        description="The Markdown content to fix."
    )

class FixMarkdownResponse(BaseModel):
    fixed_content: str = Field(description="The fixed Markdown content.")
    changes_made: List[str] = Field(description="List of changes that were made.")
    is_now_valid: bool = Field(description="Whether the fixed content passes format_check.")
    remaining_errors: List[str] = Field(description="Any remaining errors after fixes.")

@mcp_app.tool()
async def fix_markdown(ctx: Context, request: FixMarkdownRequest) -> FixMarkdownResponse:
    """
    Automatically fix common Markdown format issues based on format_check results.

    This tool will:
    1. Add missing required sections (Feature readiness status, Executive Summary, Radar List)
    2. Convert plain text to table format in Feature readiness status
    3. Add area headers (###) to Radar List if missing
    4. Add status tags to areas that are missing them

    Returns the fixed content and a list of changes made.
    """
    await ctx.info("Starting automatic Markdown fix...")

    markdown_content = request.markdown_content
    changes_made = []

    # Helper function to split sections
    def split_sections(md_text):
        sections = re.split(r'(?m)^# ', md_text)
        section_dict = {}
        section_order = []
        for section in sections:
            if not section.strip():
                continue
            lines = section.splitlines()
            title = lines[0].strip()
            content = "\n".join(lines[1:]).strip()
            section_dict[title] = content
            section_order.append(title)
        return section_dict, section_order

    sections, section_order = split_sections(markdown_content)

    # Fix 1: Add missing required sections
    required_sections = ["Feature readiness status", "Executive Summary", "Radar List"]
    for req_sec in required_sections:
        if req_sec not in sections:
            if req_sec == "Feature readiness status":
                sections[req_sec] = """| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|Feature 1|
|testable|testable|Feature 2|"""
                changes_made.append(f"Added missing section: '# {req_sec}' with template table")
            elif req_sec == "Executive Summary":
                sections[req_sec] = """- Summary item 1
    - Detail 1"""
                changes_made.append(f"Added missing section: '# {req_sec}' with template content")
            elif req_sec == "Radar List":
                sections[req_sec] = """### Area 1
- LOW
    - Completed items"""
                changes_made.append(f"Added missing section: '# {req_sec}' with template content")

    # Ensure correct section order: Feature readiness status -> Executive Summary -> Radar List
    correct_order = ["Feature readiness status", "Executive Summary", "Radar List"]
    # Keep other sections in their original order
    other_sections = [s for s in section_order if s not in correct_order]
    section_order = correct_order + other_sections

    # Fix 2: Fix Feature readiness status table format
    if "Feature readiness status" in sections:
        content = sections["Feature readiness status"]
        if content and not ('|' in content and '---' in content):
            # Convert plain text to table
            lines = [line.strip() for line in content.splitlines() if line.strip()]
            table = """| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|"""
            for line in lines[:5]:  # Take first 5 lines
                table += f"\n|testable|testable|{line}|"
            sections["Feature readiness status"] = table
            changes_made.append("Converted 'Feature readiness status' plain text to table format")

    # Fix 3: Add area headers to Radar List
    if "Radar List" in sections:
        content = sections["Radar List"]
        if content:
            area_pattern = re.compile(r'^###\s+(.+)', re.MULTILINE)
            areas = area_pattern.findall(content)

            if not areas:
                # No area headers found, add a default one
                sections["Radar List"] = f"### General\n{content}"
                changes_made.append("Added default area header '### General' to Radar List")

    # Fix 4: Add status tags to areas missing them
    if "Radar List" in sections:
        content = sections["Radar List"]
        if content:
            area_pattern = re.compile(r'^###\s+(.+)', re.MULTILINE)
            status_pattern = re.compile(r'(?:\[)?(LOW|MEDIUM|MED-HIGH|HIGH|NOT STARTED)(?:\])?', re.IGNORECASE)

            lines = content.splitlines()
            fixed_lines = []
            current_area = None
            area_has_status = False
            area_start_line = -1

            for line in lines:
                if area_pattern.match(line):
                    # Check if previous area needs a status tag
                    if current_area and not area_has_status and area_start_line >= 0:
                        # Insert a LOW status tag after the area header
                        fixed_lines.insert(area_start_line + 1, "- LOW")
                        fixed_lines.insert(area_start_line + 2, "    - Items completed")
                        changes_made.append(f"Added default status tag 'LOW' to area '{current_area}'")

                    current_area = area_pattern.match(line).group(1)
                    area_has_status = False
                    area_start_line = len(fixed_lines)
                    fixed_lines.append(line)

                elif line.strip().startswith('-'):
                    indent = len(line) - len(line.lstrip())
                    if indent <= 4:
                        line_content = line.strip()[1:].strip()
                        if status_pattern.match(line_content):
                            area_has_status = True
                    fixed_lines.append(line)
                else:
                    fixed_lines.append(line)

            # Check last area
            if current_area and not area_has_status and area_start_line >= 0:
                fixed_lines.insert(area_start_line + 1, "- LOW")
                fixed_lines.insert(area_start_line + 2, "    - Items completed")
                changes_made.append(f"Added default status tag 'LOW' to area '{current_area}'")

            sections["Radar List"] = "\n".join(fixed_lines)

    # Reconstruct the markdown
    fixed_content = ""
    for section_title in section_order:
        if section_title in sections:
            fixed_content += f"# {section_title}\n{sections[section_title]}\n\n"

    # Run format_check on the fixed content
    from test_format_check import test_format_check_logic
    check_result = test_format_check_logic(fixed_content)

    await ctx.info(f"Fix complete. Made {len(changes_made)} changes.")

    return FixMarkdownResponse(
        fixed_content=fixed_content.strip(),
        changes_made=changes_made,
        is_now_valid=check_result['is_valid'],
        remaining_errors=check_result['errors']
    )


if __name__ == "__main__":
    mcp_app.run()