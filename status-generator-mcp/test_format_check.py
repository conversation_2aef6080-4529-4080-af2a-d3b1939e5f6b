#!/usr/bin/env python3
"""
测试format_check功能
创建多个测试用例，包括正确和错误的Markdown格式
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

# 测试用例
test_cases = {
    "valid_complete": {
        "name": "✅ 完整正确的格式",
        "content": """# Feature readiness status
| Sival       | Dev                     |                 |   |
|---------------|---------------------------------|---------------------|------|
|testable|testable|USB2 Host/Device Mode|
|na|testable|Compliance|
|testable|testable|Performance|test notes, (rdar://132552351)

# Executive Summary
- SiVal board with Linux (Thera_lws_release_20250502)
    - Feature Sweep completed
- Major issue
    - [HIGH] Critical bug description
        - Detailed information

# Radar List
### USB-IOT
- LOW
    - Completed
- [HIGH]
    - Critical issue description
""",
        "expected": "valid"
    },
    
    "missing_feature_status": {
        "name": "❌ 缺少Feature readiness status部分",
        "content": """# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed

# Radar List
### USB-IOT
- [LOW]
    - Completed
""",
        "expected": "invalid"
    },
    
    "missing_executive_summary": {
        "name": "❌ 缺少Executive Summary部分",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|USB2 Host/Device Mode|

# Radar List
### USB-IOT
- [LOW]
    - Completed
""",
        "expected": "invalid"
    },
    
    "missing_radar_list": {
        "name": "❌ 缺少Radar List部分",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|USB2 Host/Device Mode|

# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed
""",
        "expected": "invalid"
    },
    
    "invalid_table_format": {
        "name": "❌ Feature readiness status表格格式错误",
        "content": """# Feature readiness status
This is not a table, just plain text.

# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed

# Radar List
### USB-IOT
- [LOW]
    - Completed
""",
        "expected": "invalid"
    },
    
    "missing_area_headers": {
        "name": "❌ Radar List缺少区域标题（###）",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|USB2 Host/Device Mode|

# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed

# Radar List
- [LOW]
    - Completed without area header
""",
        "expected": "invalid"
    },
    
    "missing_status_tags": {
        "name": "❌ Radar List缺少状态标签",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|USB2 Host/Device Mode|

# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed

# Radar List
### USB-IOT
- Completed
    - No status tag here
### Another Area
- Also no status tag
""",
        "expected": "invalid"
    },
    
    "valid_with_rdar_links": {
        "name": "✅ 包含rdar链接（新旧格式）",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|Performance|notes, (rdar://132552351)

# Executive Summary
- Major issue
    - rdar://153864687 (Sonar: Only Resource ID in CSV import)
    - Another issue (rdar://152635214)

# Radar List
### USB-IOT
- HIGH
    - Storage issue rdar://132552351 (ASMedia chipset)
""",
        "expected": "valid"
    },
    
    "valid_with_nested_lists": {
        "name": "✅ 包含深层嵌套列表",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|USB2 Host/Device Mode|

# Executive Summary
- Level 1
    - Level 2
        - Level 3
            - Level 4

# Radar List
### USB-IOT
- LOW
    - Item 1
        - Sub-item 1
            - Sub-sub-item 1
""",
        "expected": "valid"
    },
    
    "empty_sections": {
        "name": "✅ 包含空的Executive Summary",
        "content": """# Feature readiness status
| Sival | Dev | Feature Name |
|---|---|---|
|testable|testable|USB2 Host/Device Mode|

# Executive Summary

# Radar List
### USB-IOT
- LOW
    - Completed
""",
        "expected": "valid"  # 空的Executive Summary应该被接受
    }
}

def test_format_check_logic(markdown_content):
    """
    模拟format_check的逻辑
    这是从server.py中提取的简化版本
    """
    import re
    
    errors = []
    suggestions = []
    
    # 分割章节
    def split_sections(md_text):
        sections = re.split(r'(?m)^# ', md_text)
        section_dict = {}
        for section in sections:
            if not section.strip():
                continue
            lines = section.splitlines()
            title = lines[0].strip()
            content = "\n".join(lines[1:]).strip()
            section_dict[title] = content
        return section_dict
    
    sections = split_sections(markdown_content)
    
    # 检查必需的部分
    required_sections = ["Feature readiness status", "Executive Summary", "Radar List"]
    for req_sec in required_sections:
        if req_sec not in sections:
            errors.append(f"Missing required section: '# {req_sec}'")
            suggestions.append(f"Please add a top-level header '# {req_sec}' to your report.")
    
    # 检查Feature readiness status是否是表格
    if "Feature readiness status" in sections:
        content = sections["Feature readiness status"]
        if content and not ('|' in content and '---' in content):
            errors.append("'Feature readiness status' section should contain a Markdown table.")
            suggestions.append("Format the 'Feature readiness status' section as a Markdown table with columns like 'Sival', 'Dev', 'Feature Name'.")
    
    # 检查Radar List是否有区域标题和状态标签
    if "Radar List" in sections:
        content = sections["Radar List"]
        if content:
            area_pattern = re.compile(r'^###\s+(.+)', re.MULTILINE)
            areas = area_pattern.findall(content)
            if not areas:
                errors.append("'Radar List' section should have area headers (###).")
                suggestions.append("Add area headers like '### USB-IOT' in the 'Radar List' section.")

            # 支持两种格式: [LOW] 或 LOW (带或不带方括号)
            status_pattern = re.compile(r'(?:\[)?(LOW|MEDIUM|MED-HIGH|HIGH|NOT STARTED)(?:\])?', re.IGNORECASE)
            lines = content.splitlines()

            # 检查每个区域是否至少有一个状态标签
            current_area = None
            area_has_status = False

            for line in lines:
                if area_pattern.match(line):
                    # 检查上一个区域
                    if current_area and not area_has_status:
                        errors.append(f"Area '{current_area}' in 'Radar List' has no status tags.")
                        suggestions.append(f"Add at least one status tag like `[LOW]`, `[HIGH]` under area '{current_area}'.")

                    current_area = area_pattern.match(line).group(1)
                    area_has_status = False

                elif line.strip().startswith('-'):
                    # 检查是否是顶级列表项（无缩进或最小缩进）
                    indent = len(line) - len(line.lstrip())
                    if indent <= 4:  # 顶级或第一级缩进
                        # 检查这一行或其内容是否包含状态
                        line_content = line.strip()[1:].strip()  # 移除 '-' 和空格
                        if status_pattern.match(line_content):
                            area_has_status = True

            # 检查最后一个区域
            if current_area and not area_has_status:
                errors.append(f"Area '{current_area}' in 'Radar List' has no status tags.")
                suggestions.append(f"Add at least one status tag like `[LOW]`, `[HIGH]` under area '{current_area}'.")
    
    if errors:
        return {"is_valid": False, "errors": errors, "suggestions": suggestions}
    else:
        return {"is_valid": True, "errors": [], "suggestions": ["Markdown format is valid."]}

def run_tests():
    """运行所有测试用例"""
    print("=" * 80)
    print("format_check 功能测试")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_id, test_case in test_cases.items():
        print(f"\n{'=' * 80}")
        print(f"测试: {test_case['name']}")
        print(f"ID: {test_id}")
        print(f"{'=' * 80}")
        
        # 显示输入内容（截断）
        content_preview = test_case['content'][:200].replace('\n', '\\n')
        print(f"\n📄 输入内容预览:")
        print(f"   {content_preview}...")
        print(f"   总长度: {len(test_case['content'])} 字符")
        
        # 运行format_check
        result = test_format_check_logic(test_case['content'])
        
        # 显示结果
        print(f"\n📊 检查结果:")
        print(f"   is_valid: {result['is_valid']}")
        
        if result['errors']:
            print(f"\n❌ 错误 ({len(result['errors'])}):")
            for i, error in enumerate(result['errors'], 1):
                print(f"      {i}. {error}")
        
        if result['suggestions']:
            print(f"\n💡 建议 ({len(result['suggestions'])}):")
            for i, suggestion in enumerate(result['suggestions'], 1):
                print(f"      {i}. {suggestion}")
        
        # 验证结果
        expected_valid = test_case['expected'] == 'valid'
        actual_valid = result['is_valid']
        
        if expected_valid == actual_valid:
            print(f"\n✅ 测试通过")
            passed += 1
        else:
            print(f"\n❌ 测试失败")
            print(f"   期望: {'valid' if expected_valid else 'invalid'}")
            print(f"   实际: {'valid' if actual_valid else 'invalid'}")
            failed += 1
    
    # 总结
    print(f"\n{'=' * 80}")
    print(f"测试总结")
    print(f"{'=' * 80}")
    print(f"总测试数: {len(test_cases)}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"成功率: {passed / len(test_cases) * 100:.1f}%")
    
    if failed == 0:
        print(f"\n🎉 所有测试通过！format_check功能正常工作")
        return 0
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，需要检查")
        return 1

def save_test_cases_to_files():
    """将测试用例保存为独立的.md文件"""
    import os
    
    test_dir = "test_cases"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    print(f"\n📁 保存测试用例到 {test_dir}/ 目录")
    
    for test_id, test_case in test_cases.items():
        filename = f"{test_dir}/{test_id}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(test_case['content'])
        print(f"   ✅ {filename}")
    
    print(f"\n💾 已保存 {len(test_cases)} 个测试用例")

if __name__ == "__main__":
    # 运行测试
    result = run_tests()
    
    # 保存测试用例
    save_test_cases_to_files()
    
    sys.exit(result)

