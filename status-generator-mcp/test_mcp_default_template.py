#!/usr/bin/env python3
"""
测试 MCP Server 的 generate_report 默认模板功能
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

# 模拟MCP请求和响应
class MockContext:
    def __init__(self):
        self.logs = []
    
    async def info(self, message):
        print(f"INFO: {message}")
        self.logs.append(('INFO', message))
    
    async def warning(self, message):
        print(f"WARNING: {message}")
        self.logs.append(('WARNING', message))
    
    async def error(self, message):
        print(f"ERROR: {message}")
        self.logs.append(('ERROR', message))

class GenerateReportRequest:
    def __init__(self, markdown_content="", use_default_template=False, default_features=None, default_radar_areas=None):
        self.markdown_content = markdown_content
        self.use_default_template = use_default_template
        self.default_features = default_features
        self.default_radar_areas = default_radar_areas

async def test_mcp_default_template():
    """测试MCP Server的默认模板功能"""
    print("🧪 测试 MCP Server generate_report 默认模板功能")
    print("=" * 80)
    
    # 导入MCP函数
    from server import generate_report
    
    ctx = MockContext()
    
    # 测试1: 空内容，自动使用默认模板
    print("\n1. 测试空内容自动使用默认模板:")
    print("-" * 50)
    
    request1 = GenerateReportRequest(markdown_content="")
    
    try:
        response1 = await generate_report(ctx, request1)
        
        print(f"✅ 生成成功")
        print(f"   HTML长度: {len(response1.html_report):,} 字符")
        print(f"   使用模板: {response1.used_template}")
        print(f"   消息: {response1.message}")
        
        # 保存HTML
        output_file1 = Path("test_outputs/mcp_empty_content.html")
        output_file1.parent.mkdir(exist_ok=True)
        with open(output_file1, 'w', encoding='utf-8') as f:
            f.write(response1.html_report)
        print(f"   已保存: {output_file1}")
        
    except Exception as e:
        print(f"❌ 测试1失败: {e}")
    
    # 测试2: 显式使用默认模板
    print("\n2. 测试显式使用默认模板:")
    print("-" * 50)
    
    request2 = GenerateReportRequest(
        markdown_content="# Some content",  # 有内容但强制使用模板
        use_default_template=True
    )
    
    try:
        response2 = await generate_report(ctx, request2)
        
        print(f"✅ 生成成功")
        print(f"   HTML长度: {len(response2.html_report):,} 字符")
        print(f"   使用模板: {response2.used_template}")
        print(f"   消息: {response2.message}")
        
        # 保存HTML
        output_file2 = Path("test_outputs/mcp_explicit_template.html")
        with open(output_file2, 'w', encoding='utf-8') as f:
            f.write(response2.html_report)
        print(f"   已保存: {output_file2}")
        
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
    
    # 测试3: 自定义功能的默认模板
    print("\n3. 测试自定义功能的默认模板:")
    print("-" * 50)
    
    custom_features = [
        "Wireless Charging",
        "5G Connectivity", 
        "AI Processing",
        "Biometric Security"
    ]
    
    request3 = GenerateReportRequest(
        use_default_template=True,
        default_features=custom_features
    )
    
    try:
        response3 = await generate_report(ctx, request3)
        
        print(f"✅ 生成成功")
        print(f"   HTML长度: {len(response3.html_report):,} 字符")
        print(f"   使用模板: {response3.used_template}")
        
        # 检查是否包含自定义功能
        for feature in custom_features:
            if feature in response3.html_report:
                print(f"   ✅ 包含功能: {feature}")
            else:
                print(f"   ❌ 缺少功能: {feature}")
        
        # 保存HTML
        output_file3 = Path("test_outputs/mcp_custom_features.html")
        with open(output_file3, 'w', encoding='utf-8') as f:
            f.write(response3.html_report)
        print(f"   已保存: {output_file3}")
        
    except Exception as e:
        print(f"❌ 测试3失败: {e}")
    
    # 测试4: 自定义雷达区域的默认模板
    print("\n4. 测试自定义雷达区域的默认模板:")
    print("-" * 50)
    
    custom_areas = [
        "Machine Learning",
        "Cloud Integration", 
        "Data Privacy",
        "User Experience"
    ]
    
    request4 = GenerateReportRequest(
        use_default_template=True,
        default_radar_areas=custom_areas
    )
    
    try:
        response4 = await generate_report(ctx, request4)
        
        print(f"✅ 生成成功")
        print(f"   HTML长度: {len(response4.html_report):,} 字符")
        print(f"   使用模板: {response4.used_template}")
        
        # 检查是否包含自定义区域
        for area in custom_areas:
            if area in response4.html_report:
                print(f"   ✅ 包含区域: {area}")
            else:
                print(f"   ❌ 缺少区域: {area}")
        
        # 保存HTML
        output_file4 = Path("test_outputs/mcp_custom_areas.html")
        with open(output_file4, 'w', encoding='utf-8') as f:
            f.write(response4.html_report)
        print(f"   已保存: {output_file4}")
        
    except Exception as e:
        print(f"❌ 测试4失败: {e}")
    
    # 测试5: 正常内容（不使用模板）
    print("\n5. 测试正常内容（不使用模板）:")
    print("-" * 50)
    
    normal_content = """# Feature readiness status
| Sival | Dev | Feature | Notes |
|-------|-----|---------|-------|
| testable | testable | Custom Feature | |

# Executive Summary
- Custom summary content

# Radar List
### Custom Area
- HIGH
    - Custom radar content
"""
    
    request5 = GenerateReportRequest(markdown_content=normal_content)
    
    try:
        response5 = await generate_report(ctx, request5)
        
        print(f"✅ 生成成功")
        print(f"   HTML长度: {len(response5.html_report):,} 字符")
        print(f"   使用模板: {response5.used_template}")
        print(f"   消息: {response5.message}")
        
        # 检查是否包含自定义内容
        if "Custom Feature" in response5.html_report:
            print(f"   ✅ 包含自定义功能")
        if "Custom Area" in response5.html_report:
            print(f"   ✅ 包含自定义区域")
        
        # 保存HTML
        output_file5 = Path("test_outputs/mcp_normal_content.html")
        with open(output_file5, 'w', encoding='utf-8') as f:
            f.write(response5.html_report)
        print(f"   已保存: {output_file5}")
        
    except Exception as e:
        print(f"❌ 测试5失败: {e}")
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    output_files = [
        "test_outputs/mcp_empty_content.html",
        "test_outputs/mcp_explicit_template.html", 
        "test_outputs/mcp_custom_features.html",
        "test_outputs/mcp_custom_areas.html",
        "test_outputs/mcp_normal_content.html"
    ]
    
    print(f"\n✅ 生成的HTML文件:")
    for file_path in output_files:
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            print(f"  🌐 {file_path} ({size:,} 字节)")
        else:
            print(f"  ❌ {file_path} (未生成)")
    
    print(f"\n🎉 MCP Server默认模板功能测试完成！")
    print(f"📁 查看 test_outputs/ 目录中的生成文件")

def main():
    """主函数"""
    asyncio.run(test_mcp_default_template())

if __name__ == "__main__":
    main()
