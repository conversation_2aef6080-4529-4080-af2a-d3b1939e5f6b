#!/usr/bin/env python3
"""
完整工作流程测试
演示: format_check → fix_markdown → generate_report
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from test_format_check import test_format_check_logic
from test_fix_markdown import fix_markdown_logic
import subprocess
from pathlib import Path

def test_complete_workflow():
    """测试完整的工作流程"""
    print("=" * 80)
    print("完整工作流程测试")
    print("format_check → fix_markdown → generate_report")
    print("=" * 80)
    
    # 测试用例：一个有多个错误的Markdown文件
    broken_markdown = """# Executive Summary
- SiVal board with Linux
    - Feature Sweep completed

# Radar List
### USB-IOT
- Item without status tag
### Compliance
- Another item without status
"""
    
    print("\n" + "=" * 80)
    print("步骤 1: format_check - 检查原始内容")
    print("=" * 80)
    
    check_result = test_format_check_logic(broken_markdown)
    print(f"\n📄 原始内容:")
    print(broken_markdown)
    print(f"\n📊 format_check 结果:")
    print(f"   is_valid: {check_result['is_valid']}")
    print(f"   错误数: {len(check_result['errors'])}")
    for i, error in enumerate(check_result['errors'], 1):
        print(f"   {i}. {error}")
    
    if not check_result['is_valid']:
        print("\n❌ 格式检查失败，需要修复")
    
    # 步骤2: fix_markdown
    print("\n" + "=" * 80)
    print("步骤 2: fix_markdown - 自动修复")
    print("=" * 80)
    
    fix_result = fix_markdown_logic(broken_markdown)
    print(f"\n🔧 修改列表:")
    for i, change in enumerate(fix_result['changes_made'], 1):
        print(f"   {i}. {change}")
    
    print(f"\n📊 修复后的 format_check 结果:")
    print(f"   is_valid: {fix_result['is_now_valid']}")
    print(f"   剩余错误: {len(fix_result['remaining_errors'])}")
    
    if fix_result['is_now_valid']:
        print("\n✅ 修复成功！格式现在正确")
    else:
        print("\n⚠️  部分修复，仍有错误:")
        for error in fix_result['remaining_errors']:
            print(f"      - {error}")
    
    # 步骤3: generate_report
    print("\n" + "=" * 80)
    print("步骤 3: generate_report - 生成HTML报告")
    print("=" * 80)
    
    if fix_result['is_now_valid']:
        # 保存修复后的内容到临时文件
        temp_md = Path("test_outputs/workflow_test.md")
        temp_md.parent.mkdir(exist_ok=True)
        temp_md.write_text(fix_result['fixed_content'], encoding='utf-8')
        
        # 生成HTML
        temp_html = Path("test_outputs/workflow_test.html")
        try:
            result = subprocess.run(
                ['python3', 'status_generator.py', 
                 '--md', str(temp_md),
                 '--html', str(temp_html)],
                capture_output=True,
                text=True,
                check=True
            )
            
            if temp_html.exists():
                html_size = temp_html.stat().st_size
                print(f"\n✅ HTML报告生成成功!")
                print(f"   文件: {temp_html}")
                print(f"   大小: {html_size:,} 字节")
                
                # 读取并分析HTML
                html_content = temp_html.read_text(encoding='utf-8')
                
                # 统计关键元素
                table_count = html_content.count('<table')
                list_count = html_content.count('<ul')
                status_count = html_content.count('class="s')
                
                print(f"\n📊 HTML内容分析:")
                print(f"   表格数: {table_count}")
                print(f"   列表数: {list_count}")
                print(f"   状态标签数: {status_count}")
                
            else:
                print(f"\n❌ HTML文件未生成")
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 生成HTML失败:")
            print(f"   错误: {e.stderr}")
    else:
        print("\n⏭️  跳过HTML生成（格式仍有错误）")
    
    # 总结
    print("\n" + "=" * 80)
    print("工作流程总结")
    print("=" * 80)
    
    print(f"\n1️⃣  format_check:")
    print(f"   原始错误数: {len(check_result['errors'])}")
    
    print(f"\n2️⃣  fix_markdown:")
    print(f"   修改数: {len(fix_result['changes_made'])}")
    print(f"   修复后是否有效: {fix_result['is_now_valid']}")
    
    print(f"\n3️⃣  generate_report:")
    if fix_result['is_now_valid'] and temp_html.exists():
        print(f"   状态: ✅ 成功")
        print(f"   输出: {temp_html}")
    else:
        print(f"   状态: ⏭️  跳过")
    
    print("\n" + "=" * 80)
    
    if fix_result['is_now_valid'] and temp_html.exists():
        print("🎉 完整工作流程测试成功！")
        print("\n推荐的使用流程:")
        print("  1. 编写Markdown文件")
        print("  2. 运行 format_check 检查格式")
        print("  3. 如果有错误，运行 fix_markdown 自动修复")
        print("  4. 审查修复结果，手动完善内容")
        print("  5. 再次运行 format_check 确认")
        print("  6. 运行 generate_report 生成HTML")
        return 0
    else:
        print("⚠️  工作流程部分完成")
        return 1

def test_multiple_files():
    """测试多个文件的完整流程"""
    print("\n" + "=" * 80)
    print("批量文件测试")
    print("=" * 80)
    
    test_files = [
        "test_md_files/02_missing_feature_status.md",
        "test_md_files/05_invalid_table_format.md",
        "test_md_files/07_missing_status_tags.md"
    ]
    
    results = []
    
    for test_file in test_files:
        print(f"\n{'=' * 80}")
        print(f"测试文件: {test_file}")
        print(f"{'=' * 80}")
        
        # 读取文件
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. format_check
        check_result = test_format_check_logic(content)
        print(f"\n1️⃣  format_check: {'❌ 失败' if not check_result['is_valid'] else '✅ 通过'}")
        print(f"   错误数: {len(check_result['errors'])}")
        
        # 2. fix_markdown
        fix_result = fix_markdown_logic(content)
        print(f"\n2️⃣  fix_markdown:")
        print(f"   修改数: {len(fix_result['changes_made'])}")
        print(f"   修复后: {'✅ 有效' if fix_result['is_now_valid'] else '❌ 仍有错误'}")
        
        # 3. generate_report
        if fix_result['is_now_valid']:
            # 保存并生成HTML
            base_name = Path(test_file).stem
            temp_md = Path(f"test_outputs/{base_name}_fixed.md")
            temp_html = Path(f"test_outputs/{base_name}_fixed.html")
            
            temp_md.write_text(fix_result['fixed_content'], encoding='utf-8')
            
            try:
                subprocess.run(
                    ['python3', 'status_generator.py',
                     '--md', str(temp_md),
                     '--html', str(temp_html)],
                    capture_output=True,
                    text=True,
                    check=True
                )
                
                if temp_html.exists():
                    html_size = temp_html.stat().st_size
                    print(f"\n3️⃣  generate_report: ✅ 成功")
                    print(f"   大小: {html_size:,} 字节")
                    results.append({
                        'file': test_file,
                        'success': True,
                        'html_size': html_size
                    })
                else:
                    print(f"\n3️⃣  generate_report: ❌ 失败")
                    results.append({'file': test_file, 'success': False})
            except subprocess.CalledProcessError:
                print(f"\n3️⃣  generate_report: ❌ 失败")
                results.append({'file': test_file, 'success': False})
        else:
            print(f"\n3️⃣  generate_report: ⏭️  跳过")
            results.append({'file': test_file, 'success': False})
    
    # 总结
    print("\n" + "=" * 80)
    print("批量测试总结")
    print("=" * 80)
    
    success_count = sum(1 for r in results if r['success'])
    print(f"\n总文件数: {len(test_files)}")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {len(test_files) - success_count}")
    print(f"成功率: {success_count / len(test_files) * 100:.1f}%")
    
    if success_count == len(test_files):
        print("\n🎉 所有文件都成功完成完整流程！")
    
    return 0 if success_count == len(test_files) else 1

if __name__ == "__main__":
    # 运行单个文件的完整流程测试
    result1 = test_complete_workflow()
    
    # 运行批量文件测试
    result2 = test_multiple_files()
    
    sys.exit(max(result1, result2))

