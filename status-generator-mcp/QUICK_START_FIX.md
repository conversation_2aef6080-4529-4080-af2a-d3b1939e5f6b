# fix_markdown 快速开始

## 🚀 5分钟快速上手

### 1. 最简单的使用

```python
from test_fix_markdown import fix_markdown_logic

# 你的Markdown内容（有错误也没关系）
markdown = """
# Executive Summary
- Summary item

# Radar List
### USB-IOT
- Item without status
"""

# 一键修复
result = fix_markdown_logic(markdown)

# 使用修复后的内容
print(result['fixed_content'])
```

### 2. 查看修改了什么

```python
result = fix_markdown_logic(markdown)

print(f"修改数: {len(result['changes_made'])}")
for change in result['changes_made']:
    print(f"  ✓ {change}")

print(f"\n格式正确: {result['is_now_valid']}")
```

### 3. 完整流程（推荐）

```python
from test_format_check import test_format_check_logic
from test_fix_markdown import fix_markdown_logic

# 1. 检查
check = test_format_check_logic(markdown)
if not check['is_valid']:
    print(f"发现 {len(check['errors'])} 个错误")
    
    # 2. 修复
    result = fix_markdown_logic(markdown)
    print(f"进行了 {len(result['changes_made'])} 处修改")
    
    # 3. 使用修复后的内容
    if result['is_now_valid']:
        markdown = result['fixed_content']
        print("✅ 格式现在正确了！")
```

## 📋 常见问题修复

### 问题1: 缺少必需部分

**错误**: `Missing required section: '# Feature readiness status'`

**修复**: 自动添加模板表格

```markdown
# Feature readiness status
| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|Feature 1|
|testable|testable|Feature 2|
```

### 问题2: 表格格式错误

**错误**: `'Feature readiness status' section should contain a Markdown table.`

**修复**: 将纯文本转换为表格

```markdown
# 修复前
# Feature readiness status
Some plain text

# 修复后
# Feature readiness status
| Sival | Dev | Feature Name | Notes |
|-------|-----|--------------|-------|
|testable|testable|Some plain text|
```

### 问题3: 缺少区域标题

**错误**: `'Radar List' section should have area headers (###).`

**修复**: 添加默认区域标题

```markdown
# 修复前
# Radar List
- LOW
    - Items

# 修复后
# Radar List
### General
- LOW
    - Items
```

### 问题4: 缺少状态标签

**错误**: `Area 'USB-IOT' in 'Radar List' has no status tags.`

**修复**: 添加默认LOW状态

```markdown
# 修复前
# Radar List
### USB-IOT
- Item without status

# 修复后
# Radar List
### USB-IOT
- LOW
    - Items completed
- Item without status
```

## 🧪 快速测试

### 测试1: 运行单元测试

```bash
cd status-generator-mcp
python3 test_fix_markdown.py
```

**预期输出**:
```
✅ 测试通过 - 修复后格式正确 (x5)
成功率: 100.0%
🎉 所有测试通过！
```

### 测试2: 运行完整流程

```bash
python3 test_complete_workflow.py
```

**预期输出**:
```
1️⃣  format_check: 原始错误数: 3
2️⃣  fix_markdown: 修改数: 3
3️⃣  generate_report: ✅ 成功
🎉 完整工作流程测试成功！
```

### 测试3: 修复实际文件

```bash
python3 << 'EOF'
from test_fix_markdown import fix_markdown_logic

# 读取文件
with open('test_md_files/02_missing_feature_status.md', 'r') as f:
    content = f.read()

# 修复
result = fix_markdown_logic(content)

# 保存
with open('fixed.md', 'w') as f:
    f.write(result['fixed_content'])

print(f"✅ 修复完成: {len(result['changes_made'])} 处修改")
EOF
```

## 💡 实用技巧

### 技巧1: 批量修复文件

```bash
for file in test_md_files/*_missing_*.md; do
    python3 -c "
from test_fix_markdown import fix_markdown_logic
with open('$file', 'r') as f:
    result = fix_markdown_logic(f.read())
with open('${file%.md}_fixed.md', 'w') as f:
    f.write(result['fixed_content'])
print('✅ Fixed: $file')
"
done
```

### 技巧2: 只在需要时修复

```python
check = test_format_check_logic(markdown)
if not check['is_valid']:
    result = fix_markdown_logic(markdown)
    markdown = result['fixed_content']
```

### 技巧3: 保存修改历史

```python
result = fix_markdown_logic(markdown)

# 保存修改记录
with open('changes.log', 'w') as f:
    f.write(f"修改时间: {datetime.now()}\n")
    f.write(f"修改数: {len(result['changes_made'])}\n\n")
    for change in result['changes_made']:
        f.write(f"- {change}\n")
```

## 📊 返回值说明

```python
result = {
    'fixed_content': str,        # 修复后的完整Markdown
    'changes_made': List[str],   # 修改列表
    'is_now_valid': bool,        # 是否通过format_check
    'remaining_errors': List[str] # 剩余错误（如果有）
}
```

### 使用示例

```python
result = fix_markdown_logic(markdown)

# 获取修复后的内容
fixed_md = result['fixed_content']

# 查看做了哪些修改
for change in result['changes_made']:
    print(f"✓ {change}")

# 检查是否完全修复
if result['is_now_valid']:
    print("✅ 格式完全正确")
else:
    print("⚠️  仍有错误:")
    for error in result['remaining_errors']:
        print(f"  - {error}")
```

## ⚠️ 注意事项

### 1. 模板内容需要手动填写

修复后的内容使用默认模板，需要替换为实际数据：

```markdown
# 修复后（模板）
|testable|testable|Feature 1|

# 需要改为（实际数据）
|testable|testable|USB2 Host/Device Mode|
```

### 2. 默认状态是LOW

自动添加的状态标签默认为 `LOW`，根据实际情况调整：

```markdown
# 修复后
- LOW
    - Items completed

# 可能需要改为
- HIGH
    - Critical issues
```

### 3. 保留原有内容

修复不会删除现有内容，只会添加缺失的部分。

### 4. 再次检查

修复后建议再次运行 `format_check` 确认。

## 🔄 推荐工作流程

```
1. 编写Markdown
   ↓
2. format_check (检查)
   ↓
3. fix_markdown (修复) ← 如果有错误
   ↓
4. 手动完善内容
   ↓
5. format_check (再次检查)
   ↓
6. generate_report (生成HTML)
```

## 📖 更多资源

- **FIX_MARKDOWN_GUIDE.md** - 详细使用指南
- **FIX_MARKDOWN_SUMMARY.md** - 功能总结
- **README.md** - 完整文档
- **test_fix_markdown.py** - 测试代码

## 🎯 快速命令参考

```bash
# 运行测试
python3 test_fix_markdown.py

# 完整流程测试
python3 test_complete_workflow.py

# 修复单个文件
python3 -c "
from test_fix_markdown import fix_markdown_logic
with open('input.md', 'r') as f:
    result = fix_markdown_logic(f.read())
with open('output.md', 'w') as f:
    f.write(result['fixed_content'])
"

# 查看修改
python3 -c "
from test_fix_markdown import fix_markdown_logic
with open('input.md', 'r') as f:
    result = fix_markdown_logic(f.read())
for change in result['changes_made']:
    print(f'✓ {change}')
"
```

---

**开始使用 fix_markdown，让格式修复变得简单！** ✨

