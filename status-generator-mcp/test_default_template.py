#!/usr/bin/env python3
"""
测试 generate_report 的默认模板功能
"""

import sys
import os
import subprocess
from pathlib import Path
from typing import Optional, List

def generate_default_template(custom_features: Optional[List[str]] = None, custom_radar_areas: Optional[List[str]] = None) -> str:
    """Generate a default Markdown template with standard features and radar areas."""

    # Default features for USB-C/Thunderbolt development
    default_features = custom_features or [
        "USB-C Power Delivery",
        "DisplayPort Alt Mode",
        "USB 3.2 Gen 2",
        "Thunderbolt 4",
        "Power Management",
        "Sleep/Wake",
        "Compliance Testing",
        "Performance Validation"
    ]

    # Default radar areas
    default_areas = custom_radar_areas or [
        "USB-IOT",
        "Display Output",
        "Power Management",
        "Compliance",
        "Performance",
        "System Integration"
    ]

    # Generate Feature readiness status table
    feature_table = "# Feature readiness status\n"
    feature_table += "| Sival | Dev | Feature | Notes |\n"
    feature_table += "|-------|-----|---------|-------|\n"

    for feature in default_features:
        feature_table += f"| testable | testable | {feature} | |\n"

    # Generate Executive Summary
    executive_summary = """# Executive Summary
- All core features have been tested and are stable
    - Feature validation completed across all major components
    - Integration testing shows good compatibility
- System performance meets requirements
    - Power efficiency within expected parameters
    - Thermal management operating normally
- No critical issues identified in current testing cycle
    - Minor optimizations identified for future releases
    - All blocking issues have been resolved"""

    # Generate Radar List
    radar_list = "# Radar List\n"

    import random

    for i, area in enumerate(default_areas):
        # Assign mostly LOW status with occasional MEDIUM
        status = "LOW" if i < len(default_areas) - 2 else random.choice(["LOW", "MEDIUM"])

        radar_list += f"### {area}\n"
        radar_list += f"- {status}\n"

        if status == "LOW":
            radar_list += f"    - All {area.lower()} features are stable and performing as expected\n"
            radar_list += f"    - Minor optimizations being explored for future updates\n"
        else:
            radar_list += f"    - Some {area.lower()} issues under investigation\n"
            radar_list += f"    - Working on resolution with expected completion soon\n"

        radar_list += "\n"

    # Combine all sections
    template = f"{feature_table}\n{executive_summary}\n\n{radar_list}"

    return template

def test_default_template():
    """测试默认模板生成"""
    print("=" * 80)
    print("测试默认模板生成")
    print("=" * 80)
    
    # 测试1: 使用完全默认的设置
    print("\n1. 测试完全默认模板:")
    print("-" * 40)

    default_md = generate_default_template()
    print(f"生成的Markdown长度: {len(default_md)} 字符")
    print("\n前500字符预览:")
    print(default_md[:500] + "..." if len(default_md) > 500 else default_md)
    
    # 保存到文件
    output_file = Path("test_outputs/default_template.md")
    output_file.parent.mkdir(exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(default_md)
    print(f"\n✅ 已保存到: {output_file}")
    
    # 测试2: 自定义功能列表
    print("\n2. 测试自定义功能列表:")
    print("-" * 40)
    
    custom_features = [
        "USB4 Support",
        "PCIe Tunneling", 
        "Video Streaming",
        "Fast Charging"
    ]
    
    custom_md = generate_default_template(custom_features=custom_features)
    print(f"生成的Markdown长度: {len(custom_md)} 字符")
    
    # 检查是否包含自定义功能
    for feature in custom_features:
        if feature in custom_md:
            print(f"  ✅ 包含功能: {feature}")
        else:
            print(f"  ❌ 缺少功能: {feature}")
    
    # 保存到文件
    custom_output = Path("test_outputs/custom_features_template.md")
    with open(custom_output, 'w', encoding='utf-8') as f:
        f.write(custom_md)
    print(f"\n✅ 已保存到: {custom_output}")
    
    # 测试3: 自定义雷达区域
    print("\n3. 测试自定义雷达区域:")
    print("-" * 40)
    
    custom_areas = [
        "Security",
        "Networking", 
        "Audio/Video",
        "Storage"
    ]
    
    custom_radar_md = generate_default_template(custom_radar_areas=custom_areas)
    print(f"生成的Markdown长度: {len(custom_radar_md)} 字符")
    
    # 检查是否包含自定义区域
    for area in custom_areas:
        if f"### {area}" in custom_radar_md:
            print(f"  ✅ 包含区域: {area}")
        else:
            print(f"  ❌ 缺少区域: {area}")
    
    # 保存到文件
    radar_output = Path("test_outputs/custom_radar_template.md")
    with open(radar_output, 'w', encoding='utf-8') as f:
        f.write(custom_radar_md)
    print(f"\n✅ 已保存到: {radar_output}")
    
    return [output_file, custom_output, radar_output]

def test_html_generation(md_files):
    """测试HTML生成"""
    print("\n" + "=" * 80)
    print("测试HTML生成")
    print("=" * 80)
    
    for md_file in md_files:
        print(f"\n生成HTML: {md_file.name}")
        print("-" * 40)
        
        html_file = md_file.with_suffix('.html')
        
        try:
            # 使用status_generator.py生成HTML
            result = subprocess.run([
                'python3', 'status_generator.py',
                '--md', str(md_file),
                '--html', str(html_file)
            ], capture_output=True, text=True, check=True)
            
            if html_file.exists():
                html_size = html_file.stat().st_size
                print(f"  ✅ HTML生成成功: {html_size:,} 字节")
                
                # 简单验证HTML内容
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查关键元素
                checks = [
                    ('<table', '表格'),
                    ('<span class="sl"', 'LOW状态样式'),
                    ('Feature readiness status', '功能状态标题'),
                    ('Executive Summary', '执行摘要标题'),
                    ('Radar List', '雷达列表标题')
                ]
                
                for check, desc in checks:
                    count = html_content.count(check)
                    if count > 0:
                        print(f"    ✅ {desc}: {count} 个")
                    else:
                        print(f"    ❌ {desc}: 未找到")
                        
            else:
                print(f"  ❌ HTML文件未生成")
                
        except subprocess.CalledProcessError as e:
            print(f"  ❌ 生成失败: {e.stderr}")
        except Exception as e:
            print(f"  ❌ 错误: {e}")

def test_format_validation(md_files):
    """测试格式验证"""
    print("\n" + "=" * 80)
    print("测试格式验证")
    print("=" * 80)
    
    # 导入format_check逻辑
    from test_format_check import test_format_check_logic
    
    for md_file in md_files:
        print(f"\n验证格式: {md_file.name}")
        print("-" * 40)
        
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        result = test_format_check_logic(content)
        
        if result['is_valid']:
            print(f"  ✅ 格式正确")
        else:
            print(f"  ❌ 格式错误:")
            for error in result['errors']:
                print(f"    - {error}")

def main():
    """主测试函数"""
    print("🧪 测试 generate_report 默认模板功能")
    print("=" * 80)
    
    # 测试1: 模板生成
    md_files = test_default_template()
    
    # 测试2: HTML生成
    test_html_generation(md_files)
    
    # 测试3: 格式验证
    test_format_validation(md_files)
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    print(f"\n✅ 生成的文件:")
    for md_file in md_files:
        html_file = md_file.with_suffix('.html')
        print(f"  📄 {md_file}")
        if html_file.exists():
            print(f"  🌐 {html_file}")
    
    print(f"\n🎉 默认模板功能测试完成！")
    print(f"📁 查看 test_outputs/ 目录中的生成文件")

if __name__ == "__main__":
    main()
